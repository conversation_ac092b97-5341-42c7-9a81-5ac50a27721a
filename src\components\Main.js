// import React from 'react'; // Not needed in React 17+
import { useState, useEffect } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from './Footer';
import Sample from './Sample';
import MicReview from './MicReview';
import QualityControl from './QualityControl';
import LabReportInterface from './LabReportInterface';
import Settings from './Settings';


function Main() {
  const [currentPage, setCurrentPage] = useState('Dashboard');
  const [selectedLabId, setSelectedLabId] = useState(null);

  // 处理从Isolate Control跳转到Mic Control
  const handleNavigateToReview = (labId) => {
    setSelectedLabId(labId);
    setCurrentPage('Mic Control');
  };

  // 处理从Mic Control返回Isolate Control
  const handleNavigateBack = () => {
    setSelectedLabId(null);
    setCurrentPage('Isolate Control');
  };

  // 开发环境下添加测试工具到全局
  // useEffect(() => {
  //   if (process.env.NODE_ENV === 'development') {
  //     window.NavigationTests = NavigationTests;
  //     window.testNavigation = {
  //       currentPage,
  //       selectedLabId,
  //       navigateToReview: handleNavigateToReview,
  //       navigateBack: handleNavigateBack
  //     };
  //     console.log('🔧 测试工具已加载:');
  //     console.log('- NavigationTests.runAllTests() - 运行所有测试');
  //     console.log('- testNavigation - 查看当前导航状态');
  //   }
  // }, [currentPage, selectedLabId]);

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'Isolate Control':
        return <Sample onNavigateToReview={handleNavigateToReview} />;
      case 'Mic Control':
        return <MicReview selectedLabId={selectedLabId} onNavigateBack={handleNavigateBack} />;
      case 'Quality Control':
        return <LabReportInterface />;
      case 'Settings':
        return <Settings />;
      
      case 'Dashboard':
      default:
        return renderDashboard();
    }
  };

  const renderDashboard = () => (
    <div className="p-8">
      {/* Dashboard Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dashboard
        </h1>
        <p className="text-gray-600">
          Welcome to bioMérieux Advanced Expert System™
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Samples</p>
              <p className="text-2xl font-bold text-blue-600">247</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🧪</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Results</p>
              <p className="text-2xl font-bold text-orange-600">18</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">⏳</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Today</p>
              <p className="text-2xl font-bold text-green-600">156</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">✅</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Alerts</p>
              <p className="text-2xl font-bold text-red-600">3</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🚨</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold">S1</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Sample S2024-001 processed</p>
                  <p className="text-sm text-gray-600">Blood culture - Positive result</p>
                </div>
                <span className="text-sm text-gray-500">2 min ago</span>
              </div>

              <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-semibold">Q1</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Quality control passed</p>
                  <p className="text-sm text-gray-600">Daily QC batch completed</p>
                </div>
                <span className="text-sm text-gray-500">15 min ago</span>
              </div>

              <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-semibold">A1</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Alert: Reagent low</p>
                  <p className="text-sm text-gray-600">Station 3 - Replace reagent pack</p>
                </div>
                <span className="text-sm text-gray-500">1 hour ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                New Sample Entry
              </button>
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors">
                View Pending Results
              </button>
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors">
                Generate Report
              </button>
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors">
                System Settings
              </button>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">System Status</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Database</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm text-green-600">Online</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Instruments</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm text-green-600">4/4 Active</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Network</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span className="text-sm text-yellow-600">Slow</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 flex flex-col">
      {/* Header - Fixed at top */}
      <div className="w-full">
        <Header />
      </div>

      {/* Main Layout - Flex container for sidebar and content */}
      <div className="flex flex-1 w-full h-full">
        {/* Sidebar - Fixed width */}
        <div className="flex-shrink-0">
          <Sidebar currentPage={currentPage} setCurrentPage={setCurrentPage} />
        </div>

        {/* Main Content Area - Takes remaining space */}
        <main className="flex-1 overflow-auto w-full">
          {renderCurrentPage()}
        </main>
      </div>

      {/* Footer - Fixed at bottom */}
      <div className="w-full">
        <Footer />
      </div>
    </div>
  );
}

export default Main;