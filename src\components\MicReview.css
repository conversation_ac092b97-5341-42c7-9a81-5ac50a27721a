/* MicReview.css - 微生物检测结果审核页面样式 */

.mic-review-container {
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 1rem;
}

.mic-review-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  height: calc(100vh - 8rem);
}

.sample-info-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow-y: auto;
}

.ast-results-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.ast-table-container {
  flex: 1;
  overflow: auto;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.ast-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.ast-table th {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 10;
}

.ast-table td {
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  color: #1f2937;
}

.ast-table tbody tr:nth-child(even) {
  background: #f9fafb;
}

.ast-table tbody tr:hover {
  background: #f3f4f6;
}

.interpretation-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  min-width: 2rem;
}

.interpretation-s {
  background: #dcfce7;
  color: #166534;
}

.interpretation-r {
  background: #fee2e2;
  color: #991b1b;
}

.interpretation-i {
  background: #fef3c7;
  color: #92400e;
}

.interpretation-- {
  background: #f3f4f6;
  color: #6b7280;
  font-style: italic;
}

.phenotype-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.phenotype-checkbox input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  accent-color: #3b82f6;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.action-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-approve {
  background: #10b981;
  color: white;
}

.btn-approve:hover {
  background: #059669;
}

.btn-modify {
  background: #f59e0b;
  color: white;
}

.btn-modify:hover {
  background: #d97706;
}

.btn-reject {
  background: #ef4444;
  color: white;
}

.btn-reject:hover {
  background: #dc2626;
}

.btn-save {
  background: #6b7280;
  color: white;
}

.btn-save:hover {
  background: #4b5563;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-reviewed {
  background: #dcfce7;
  color: #166534;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
}

.info-value {
  color: #1f2937;
  font-size: 0.875rem;
  text-align: right;
  max-width: 60%;
}

.section-header {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.panel-section {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.panel-section:last-child {
  border-bottom: none;
}

.comment-box {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
  margin-top: 0.5rem;
}

.button-group {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.btn-small {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.btn-edit {
  background: #3b82f6;
  color: white;
}

.btn-clear {
  background: #d1d5db;
  color: #374151;
}

.question-text {
  color: #dc2626;
  font-weight: 500;
  font-size: 0.875rem;
}

/* 导入按钮样式 */
.import-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.import-button:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  width: 24rem;
  max-width: 90vw;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.file-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-drop-zone:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.file-drop-zone.dragover {
  border-color: #3b82f6;
  background: #eff6ff;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-buttons {
  display: flex;
  gap: 0.75rem;
}

.refresh-button {
  background: #f3f4f6;
  color: #374151;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

@media (max-width: 1024px) {
  .mic-review-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .ast-table {
    font-size: 0.75rem;
  }

  .ast-table th,
  .ast-table td {
    padding: 0.5rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
