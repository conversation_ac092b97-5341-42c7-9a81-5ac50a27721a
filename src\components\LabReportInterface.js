import React, { useState, useEffect } from 'react';
import './LabReportInterface.css';
import PhenotypeChartModal from './PhenotypeChartModal';
import { ShieldQuestion,Construction} from 'lucide-react';
import { antibioticsDataStore } from '../models/AntibioticsDataStore';
import { resultsDataStore } from '../models/ResultsDataStore';
import { distributionsDataStore } from '../models/DistributionsDataStore';
import { useSharedState } from '../SharedStateContext';
import { Tooltip,Button } from 'antd';

const LabReportInterface = ({ handleNavigation }) => {
   
  const [phenotypeModalVisible, setPhenotypeModalVisible] = useState(false);
  const [isPhenotypeModalOpen, setIsPhenotypeModalOpen] = useState(false);
  const [antimicrobialAgents, setAntimicrobialAgents] = useState(antibioticsDataStore.getData() || []);
  const [results, setResults] = useState(resultsDataStore.getResults());
  const[micDistributionData,setMicDistributionData] = useState(distributionsDataStore.getData() || []);
 const [editingValue, setEditingValue] = useState(''); // Add missing state for editingValue
  const [counter, setCounter] = useState(0);
  const [loading, setLoading] = useState(false);
  const { state, dispatch } = useSharedState();
  const [error, setError] = useState('');
  const BASE_URL = process.env.REACT_APP_BASE_URL || '';
  const openPhontypechart = () => setIsPhenotypeModalOpen(true);
  const closePhontypechart = () => setIsPhenotypeModalOpen(false);
  // Add missing state for phenotype and organism modal and selectedPhenotypes
  const [selectedPhenotypes, setSelectedPhenotypes] = useState([
    'BETA-LACTAMS',
    'IMIPENEM/CILASTATIN (+R5R OR +44 AmpC)',
    'MEROPENEM (+R5R OR +ESBLs)',
    'AMINOGLYCOSIDES',
    'RESISTANT GEN FOR TOB NET AMK'
  ]);

  const [organismModalVisible, setOrganismModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  
 
  const DeductionItem = ({ deduction, index }) => {
    const [showFull, setShowFull] = useState(false); // useState 必须始终被调用
  
    if (!deduction || typeof deduction !== "string") return null;
  
    // 解析文本
    const match = deduction.match(/（1）(.*?)(（2）|$)/s);
    const shortText = match ? match[1].trim() : deduction; 
  
    return (
      <div className="remark-input" style={{ width: "100%" }}>
        <span>{shortText}</span>
        {!showFull && (
          <Button type="link" onClick={() => setShowFull(true)}>详细...</Button>
        )}
        {showFull && (
          <div style={{ marginTop: "5px", whiteSpace: "pre-wrap" }}>{deduction}</div>
        )}
      </div>
    );
  };

  const handleMicValueChange = async (index, value, selectedAgent) => {
    // 先获取旧的 micValue 和 in-range 状态
    const prevItem = antimicrobialAgents[index];
    let prevInRange = false;
    let newInRange = false;
    const micDist = micDistributionData.find(d => {
      if (!selectedAgent || !d.displayName) return false;
      try {
        const regex = new RegExp(selectedAgent, 'i');
        return regex.test(d.displayName);
      } catch {
        return false;
      }
    });
    if (micDist) {
      const prevNum = parseFloat(prevItem.micValue);
      if (!isNaN(prevNum)) {
        prevInRange = (micDist.max !== undefined && prevNum <= micDist.max) && (micDist.min !== undefined && prevNum >= micDist.min);
      }
      const newNum = parseFloat(value);
      if (!isNaN(newNum)) {
        newInRange = (micDist.max !== undefined && newNum <= micDist.max) && (micDist.min !== undefined && newNum >= micDist.min);
      }
    }
    setAntimicrobialAgents(prev => {
      const updated = prev.map((item, i) =>
        i === index ? { ...item, micValue: value } : item
      );
      antibioticsDataStore.setData(updated);
      return updated;  
    });
    // 计数器逻辑
    setCounter(c => {
      if (!prevInRange && newInRange) return Math.max(0, c - 1);
      if (prevInRange && !newInRange) return c + 1;
      return c;
    });
    if (!selectedAgent) return;
    try {
      const resp = await fetch(`${BASE_URL}api/breaking-interpret?selectedAgent=${encodeURIComponent(selectedAgent)}&micValue=${encodeURIComponent(value)}&bacteriaId=${encodeURIComponent(state.mic[0].BacteriaId)}&bacteriaName=${encodeURIComponent(state.mic[0].BacteriaName)}&selectedMethod=MIC`);
      if (resp.ok) {
        const data = await resp.json();
        if (data.sensitivity) {
          setAntimicrobialAgents(prev => {
            const updated = prev.map((item, i) =>
              i === index ? { ...item, sensitivity: data.sensitivity } : item
            );
            antibioticsDataStore.setData(updated);
            return updated;
          });
          setResults(prevResults => {
            const found = prevResults.some(r => r.selectedAgent === selectedAgent);
            let updatedResults;
            if (found) {
              updatedResults = prevResults.map(r => {
                if (r.selectedAgent === selectedAgent) {
                  return {
                    ...r,
                    micValue: value,
                    breaking: [{ sensitivity: data.sensitivity }]
                  };
                }
                return r;
              });
            } else {
              updatedResults = [
                ...prevResults,
                {
                  selectedAgent,
                  micValue: value,
                  breaking: [{ sensitivity: data.sensitivity }]
                }
              ];
            }
            resultsDataStore.setResults(updatedResults);
            return updatedResults;
          });
          if (state.mic && Array.isArray(state.mic)) {
            const found = state.mic.some(m => m.Agent === selectedAgent);
            let updatedMic;
            if (found) {
              updatedMic = state.mic.map(m => {
                if (m.Agent === selectedAgent) {
                  return {
                    ...m,
                    MIC: value,
                    SORT: data.sensitivity
                  };
                }
                return m;
              });
            } else {
              updatedMic = [
                ...state.mic,
                {
                  Agent: selectedAgent,
                  MIC: value,
                  SORT: data.sensitivity
                }
              ];
            }
            dispatch({ type: 'SET_MIC', payload: updatedMic });
          }
        }
      }
    } catch (err) {
      console.error('Failed to fetch sensitivity:', err);
    }
  }



  useEffect(() => {
    console.log('state.patient:', state.patient);
    if (state.patient && state.patient.bacteriaCode) {
      setLoading(true);
      // 优先用 distributionsDataStore
      const cached = distributionsDataStore.getData();
      // console.log('Using cached mic distribution data:', cached);
      if (cached && Array.isArray(cached) && cached.length > 0) {
      
        setMicDistributionData(cached);
        setLoading(false);
      } else {
        fetch(`${BASE_URL}api/mic-distribution?bacteria=${state.patient.bacteriaCode}`)
          .then(res => res.json())
          .then(async data => {
            // Extract antibiotic codes (e.g., 'AMC' from 'AMC_ND20')
            const codes = Array.from(new Set((data || []).map(item => (item.antibiotic || '').split('_')[0])));
            // Fetch display names for all codes in parallel
            const displayNameMap = {};
            await Promise.all(
              codes.map(async code => {
                try {
                  const res = await fetch(`${BASE_URL}antibiotics/${code.toUpperCase()}`);
                  if (res.ok) {
                    const abx = await res.json();
                    displayNameMap[code] = abx.DISPLAY_NAME || code;
                  } else {
                    displayNameMap[code] = code;
                  }
                } catch {
                  displayNameMap[code] = code;
                }
              })
            );
            // Attach display name to each data row
            const dataWithDisplay = (data || []).map(item => ({
              ...item,
              displayName: displayNameMap[(item.antibiotic || '').split('_')[0]] || (item.antibiotic || '').split('_')[0]
            }));
            setMicDistributionData(dataWithDisplay);
            distributionsDataStore.setData(dataWithDisplay); // 存入 store
            // console.log('Fetched mic distribution data:',distributionsDataStore.getData());
            setLoading(false);
          })
          .catch(err => {
            console.error('Error fetching antimicrobial agents:', err);
            setError('加载抗菌药物数据失败');
            setLoading(false);
          });
      }
    } else {
      setMicDistributionData([]);
    }
  }, [state.patient?.bacteriaCode, BASE_URL]);

  useEffect(() => {
    let cnt = 0;
    for (const item of antimicrobialAgents) {
      const micDist = micDistributionData.find(d => {
        if (!item.name || !d.displayName) return false;
        try {
          const regex = new RegExp(item.name, 'i');
          return regex.test(d.displayName);
        } catch {
          return false;
        }
      });
      if (micDist && item.micValue) {
        const micNum = parseFloat(item.micValue);
        if (!isNaN(micNum)) {
          if ((micDist.max !== undefined && micNum > micDist.max) || (micDist.min !== undefined && micNum < micDist.min)) {
            cnt++;
          }
        }
      }
    }
    setCounter(cnt);
  }, [antimicrobialAgents, micDistributionData]);

 if(!state.patient)
   return (
     <div className="report-container">
       <div className="patient-selection-content">
         <ShieldQuestion size={35} />
         <span className="patient-selection-text">还没有选择标本信息!!</span>
       </div>
     </div>
   );

     
    
    if(!state.mic)
    
    return (
      <div className="report-container">
        <div className="patient-selection-content">
          <Construction size={35} />
          <span className="patient-selection-text">还没有生成报告,请转到'Mic折点',查询后，点击生成报告!!</span>
        </div>
      </div>
    )

    // console.log('Mic Distribution Data:', micDistributionData);
  // console.log('Antimicrobial Agents:', antimicrobialAgents);
    // console.log(results)
  // Count out-of-range MICs
const now = new Date();
const pad = n => n.toString().padStart(2, '0');
const timeStr = `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;

  return (
    <div className="lab-report-container">
      {/* Header Section */}
      <div className="lab-report-header-section">
        {/* Left Column */}
        <div className="lab-report-left-column">
          <div className="lab-report-field-row">
              <span className="lab-report-label">药敏结论:</span>
              {counter > 0 ? (
                <span className="lab-report-red-badge">需调整</span>
              ) : (
                <span className="lab-report-green-badge">与修正一致</span>
              )}
            </div>
            <div className="lab-report-field-row">
              <span className="lab-report-label">审核状态:</span>
              <span>{counter > 0 ? '待审核' : '已审核'}</span>
            </div>
            <div className="lab-report-field-row">
              <span className="lab-report-label">鉴定置信度:</span>
              <span>{counter > 0 ? '等待调整' : '鉴定结果优秀'}</span>
            </div>
            <div className="lab-report-field-row">
              <span className="lab-report-label">分析状态:</span>
              <span>{counter > 0 ? `${timeStr} - 进行中` : `${timeStr} - 终篇`}</span>
            </div>
            {/* 表型区块：insufficient 红色checkbox */}
            <div className="lab-report-field-row" >
              <span className="lab-report-label">表型分析:</span>
              <span>
                <input 
                  type="checkbox" 
                  style={{ accentColor: 'red', cursor: 'pointer' }} 
                  onClick={() => setIsPhenotypeModalOpen(true)}
                />
                <span style={{ color: 'red', marginLeft: 4, fontWeight: 500 }}>inconsistant</span>
              </span>
          <PhenotypeChartModal
            open={isPhenotypeModalOpen}
            onClose={closePhontypechart}
            antimicrobialAgents={antimicrobialAgents}
            micDistributionData={micDistributionData}
          />
            </div>
        </div>
        {/* Right Columns Group: 结论+表型 */}
        <div className="lab-report-right-columns-group" style={{display: 'flex', gap: '32px', alignItems: 'flex-start'}}>
          {/* Right Column */}
        
          {/* Phenotype Column - its own column, not below */}
          <div className="lab-report-phenotype-column" style={{minWidth: '180px'}}>
            <span className="lab-report-label">已选表型:</span>
            <div className="lab-report-phenotype-list">
              {selectedPhenotypes.map((phenotype, index) => (
                <div key={index} className="lab-report-phenotype-item">
                  <input type="checkbox" checked readOnly className="lab-report-checkbox" />
                  <span style={{fontSize: '10px'}}>{phenotype}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      {/* Analysis Messages Section: move here for vertical alignment with left column */}
      <div className="lab-report-messages-section">
          <span className="lab-report-label" style={{ marginBottom: 4 }}>分析提示:</span>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2px', marginLeft: 0 }}>
            <div style={{ display: 'flex', alignItems: 'center', minHeight: 24 }}>
              <span style={{ width: 72, color: '#888', fontSize: 13 }}>标本编号：</span>
              <span style={{ color: '#333', fontSize: 13 }}>{state?.patient?.specimenNumber || '-'}</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', minHeight: 24 }}>
              <span style={{ width: 72, color: '#888', fontSize: 13 }}>菌落计数：</span>
              <span style={{ color: '#333', fontSize: 13 }}>{state?.patient?.bacteriaCount || '-'}</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', minHeight: 24 }}>
              <span style={{ width: 72, color: '#888', fontSize: 13 }}>培养结果：</span>
              <span style={{ color: '#333', fontSize: 13 }}>{state?.patient?.cultureResult || '-'}</span>
            </div>
          </div>
        </div>
      {/* BioFIT Comment Section */}
      <div className="lab-report-comment-section" style={{paddingLeft: 0}}>
        <div className="lab-report-messages-section" style={{justifyContent: 'flex-start'}}>
          <span className="lab-report-label">BioFIT 备注:</span>
          <button className="lab-report-blue-button">M1</button>
          <button className="lab-report-gray-button">**</button>
          <button className="lab-report-gray-button">ESC</button>
        </div>
        <div className="lab-report-comment-text">
              <div className="notes">
  {/* Track a total index across all loops */}
  {(() => {
    let totalIndex = 0; // Initialize a total counter
    
    return (
      <>
        {/* Loop for Comments */}
        {[...new Set(state.mic.map(item => item.Comments))] // 去重
          .filter(comment => 
            comment && // Ensure comment is not empty
            state.mic.some(item => comment.includes(item.selectedAuthority)) && // Check for selectedAuthority
            /[\u4e00-\u9fa5]/.test(comment) // Ensure it contains Chinese characters
          )
          .map((comment, index) => {
            totalIndex++; // Increment the total index for each non-empty comment
            return (
              <div key={`comment-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {comment}
              </div>
            );
          })
        }

        {/* Loop for Remarks */}
        {[...new Set(state.mic.map(item => item.Remark))] 
          .filter(remark => remark) // Filter out empty remarks
          .map((remark, index) => {
            totalIndex++; // Increment the total index for each valid remark
            return (
              <div key={`remark-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {remark}
              </div>
            );
          })
        }

        {/* Loop for drugDeduction */}
        {[...new Set(state.mic.map(item => item.drugDeduction))] 
          .filter(deduction => deduction) // Filter out empty deductions
          .map((deduction, index) => {
            totalIndex++; // Increment the total index for each valid deduction
            return (
              <DeductionItem key={`deduction-${totalIndex}`} deduction={deduction} index={totalIndex} />
            );
          })
        }

        {/* Loop for ESBL Alerts */}
        {[...new Set(state.mic.map(item => item.esblAlert))] 
          .filter(esblAlert => esblAlert) // Filter out empty alerts
          .map((esblAlert, index) => {
            totalIndex++; // Increment the total index for each valid ESBL alert
            return (
              <div key={`esblAlert-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {esblAlert}
              </div>
            );
          })
        }

        {/* Loop for expertics (ABX_RESULT and MICROBIOL) */}
        {
          [
            ...new Set(
              state.mic.flatMap(item =>
                Array.isArray(item.expertics)
                  ? item.expertics.map(expertic =>
                      JSON.stringify({
                        ABX_RESULT: expertic.ABX_RESULT,
                        MICROBIOL: expertic.MICROBIOL
                      })
                    )
                  : []
              )
            )
          ]
          .filter(entry => entry)
          .map((uniqueEntryString, index) => {
            totalIndex++;
            const uniqueEntry = JSON.parse(uniqueEntryString);
            return (
              <div key={`expertic-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {uniqueEntry.ABX_RESULT} : {uniqueEntry.MICROBIOL}
              </div>
            );
          })
        }

              </>
            );
        })()}
</div>
        </div>
      </div>
      {/* AST Offline Tests Section */}
      <div className="lab-report-table-section">
        {/* Antibiotic Table */}
        <div className="lab-report-table-grid">
          {/* Table rendering based on antimicrobialAgents.length */}
          {(() => {
  let globalIndex = 0;
  const n = antimicrobialAgents.length;
  if (n === 0) return null;
  // 1 table if < 15
  if (n < 15) {
    return (
      <div>
        <table className="lab-report-table">
          <thead className="lab-report-table-header">
            <tr>
              <th className="lab-report-th">Antibiotic</th>
              <th className="lab-report-th">MIC</th>
              <th className="lab-report-th">Interp...</th>
            </tr>
          </thead>
          <tbody>
            {antimicrobialAgents.map((item, index) => {
              let sensitivity = '-';
              for (const result of results) {
                if (result.selectedAgent === item.name && result.breaking && result.breaking.length > 0) {
                  sensitivity = result.breaking[0].sensitivity || '-';
                  break;
                }
              }
              // Find corresponding micDistributionData entry
              const micDist = micDistributionData.find(d => {
                if (!item.name || !d.displayName) return false;
                try {
                  const regex = new RegExp(item.name, 'i');
                  const match = regex.test(d.displayName);
                  return match;
                } catch {
                  return false;
                }
              });
              let micClass = '';
              if (micDist && item.micValue) {
                const micNum = parseFloat(item.micValue);
                if (!isNaN(micNum)) {
                  if (micDist.max !== undefined && micNum > micDist.max) micClass = 'lab-report-mic-green';
                  if (micDist.min !== undefined && micNum < micDist.min) micClass = 'lab-report-mic-green';
                }
              }
              const rowIndex = globalIndex++;
              return (
                <tr key={rowIndex} className="lab-report-table-row">
                  <td className="lab-report-td">{item.name}</td>
                  <td className={`lab-report-td ${micClass}`}
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setEditingIndex(rowIndex);
                        setEditingValue(item.micValue || '');
                        // console.log('Clicked MIC cell:', { rowIndex, value: item.micValue, editingIndex, editingValue });
                      }}
                      style={{ cursor: 'pointer' }}
                  >
                    {editingIndex === rowIndex ? (
                      <input
                        type="text"
                        value={editingValue}
                        autoFocus
                        onChange={e => setEditingValue(e.target.value)}
                        onBlur={() => {
                          handleMicValueChange(rowIndex, editingValue, item.name);
                          setEditingIndex(null);
                        }}
                        onKeyDown={e => {
                          if (e.key === 'Enter') {
                            handleMicValueChange(rowIndex, editingValue, item.name);
                            setEditingIndex(null);
                          }
                        }}
                        style={{ width: '60px' }}
                      />
                    ) : (
                      item.micValue || '-'
                    )}
                  </td>
                  <td className="lab-report-td">
                    {(() => {
                      let sensitivity = '-';
                      for (const result of results) {
                        if (result.selectedAgent === item.name && result.breaking && result.breaking.length > 0) {
                          sensitivity = result.breaking[0].sensitivity || '-';
                          break;
                        }
                      }
                      return (
                        <span className={sensitivity === 'R' ? 'lab-report-resistant-text' : sensitivity === 'S' ? 'lab-report-sensitive-text' : ''}>
                          {sensitivity}
                        </span>
                      );
                    })()}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  }
  // 2 tables if 15 <= n < 30
  if (n >= 15 && n < 30) {
    const mid = Math.ceil(n / 2);
    return [0, 1].map(i => (
      <div key={i}>
        <table className="lab-report-table">
          <thead className="lab-report-table-header">
            <tr>
              <th className="lab-report-th">Antibiotic</th>
              <th className="lab-report-th">MIC</th>
              <th className="lab-report-th">Interp...</th>
            </tr>
          </thead>
          <tbody>
            {antimicrobialAgents.slice(i === 0 ? 0 : mid, i === 0 ? mid : n).map((item, index) => {
              let sensitivity = '-';
              for (const result of results) {
                if (result.selectedAgent === item.name && result.breaking && result.breaking.length > 0) {
                  sensitivity = result.breaking[0].sensitivity || '-';
                  break;
                }
              }
              // Find corresponding micDistributionData entry
              const micDist = micDistributionData.find(d => {
                if (!item.name || !d.displayName) return false;
                try {
                  const regex = new RegExp(item.name, 'i');
                  const match = regex.test(d.displayName);
                  return match;
                } catch {
                  return false;
                }
              });
              let micClass = '';
              if (micDist && item.micValue) {
                const micNum = parseFloat(item.micValue);
                if (!isNaN(micNum)) {
                  if (micDist.max !== undefined && micNum > micDist.max) micClass = 'lab-report-mic-green';
                  if (micDist.min !== undefined && micNum < micDist.min) micClass = 'lab-report-mic-green';
                }
              }
              const rowIndex = globalIndex++;
              return (
                <tr key={rowIndex} className="lab-report-table-row">
                  <td className="lab-report-td">{item.name}</td>
                  <td className={`lab-report-td ${micClass}`}
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setEditingIndex(rowIndex);
                        setEditingValue(item.micValue || '');
                        // console.log('Clicked MIC cell:', { rowIndex, value: item.micValue, editingIndex, editingValue });
                      }}
                      style={{ cursor: 'pointer' }}
                  >
                    {editingIndex === rowIndex ? (
                      <input
                        type="text"
                        value={editingValue}
                        autoFocus
                        onChange={e => setEditingValue(e.target.value)}
                        onBlur={() => {
                          handleMicValueChange(rowIndex, editingValue, item.name);
                          setEditingIndex(null);
                        }}
                        onKeyDown={e => {
                          if (e.key === 'Enter') {
                            handleMicValueChange(rowIndex, editingValue, item.name);
                            setEditingIndex(null);
                          }
                        }}
                        style={{ width: '60px' }}
                      />
                    ) : (
                      item.micValue || '-'
                    )}
                  </td>
                  <td className="lab-report-td">
                    {(() => {
                      let sensitivity = '-';
                      for (const result of results) {
                        if (result.selectedAgent === item.name && result.breaking && result.breaking.length > 0) {
                          sensitivity = result.breaking[0].sensitivity || '-';
                          break;
                        }
                      }
                      return (
                        <span className={sensitivity === 'R' ? 'lab-report-resistant-text' : sensitivity === 'S' ? 'lab-report-sensitive-text' : ''}>
                          {sensitivity}
                        </span>
                      );
                    })()}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    ));
  }
  // 3 tables if n >= 30
  if (n >= 30) {
    const part = Math.ceil(n / 3);
    return [0, 1, 2].map(i => (
      <div key={i}>
        <table className="lab-report-table">
          <thead className="lab-report-table-header">
            <tr>
              <th className="lab-report-th">Antibiotic</th>
              <th className="lab-report-th">MIC</th>
              <th className="lab-report-th">Interp...</th>
            </tr>
          </thead>
          <tbody>
            {antimicrobialAgents.slice(i * part, (i + 1) * part).map((item, index) => {
              let sensitivity = '-';
              for (const result of results) {
                if (result.selectedAgent === item.name && result.breaking && result.breaking.length > 0) {
                  sensitivity = result.breaking[0].sensitivity || '-';
                  break;
                }
               
              }
              // Find corresponding micDistributionData entry
              const micDist = micDistributionData.find(d => {
                if (!item.name || !d.displayName) return false;
                try {
                  const regex = new RegExp(item.name, 'i');
                  const match = regex.test(d.displayName);
                  return match;
                } catch {
                  return false;
                }
              });
              let micClass = '';
              if (micDist && item.micValue) {
                const micNum = parseFloat(item.micValue);
                if (!isNaN(micNum)) {
                  if (micDist.max !== undefined && micNum > micDist.max) micClass = 'lab-report-mic-green';
                  if (micDist.min !== undefined && micNum < micDist.min) micClass = 'lab-report-mic-green';
                }
              }
              const rowIndex = globalIndex++;
              // const closestDist = getClosestDistribution(micDist, item.micValue);
              // setMicDistribution(prev => [...prev, closestDist]);
              // console.log('Closest Distribution:', closestDist);
              // console.log(item);
         
              return (
                <tr key={rowIndex} className="lab-report-table-row">
                  <td className="lab-report-td">{item.name}</td>
                  <td className={`lab-report-td ${micClass}`}
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setEditingIndex(rowIndex);
                        setEditingValue(item.micValue || '');
                        // console.log('Clicked MIC cell:', { rowIndex, value: item.micValue, editingIndex, editingValue });
                      }}
                      style={{ cursor: 'pointer' }}
                  >
                    {editingIndex === rowIndex ? (
                      <input
                        type="text"
                        value={editingValue}
                        autoFocus
                        onChange={e => setEditingValue(e.target.value)}
                        onBlur={() => {
                          handleMicValueChange(rowIndex, editingValue, item.name);
                          setEditingIndex(null);
                        }}
                        onKeyDown={e => {
                          if (e.key === 'Enter') {
                            handleMicValueChange(rowIndex, editingValue, item.name);
                            setEditingIndex(null);
                          }
                        }}
                        style={{ width: '60px' }}
                      />
                    ) : (
                      item.micValue || '-'
                    )}
                  </td>
                  <td className="lab-report-td">
                    {(() => {
                      let sensitivity = '-';
                      for (const result of results) {
                        if (result.selectedAgent === item.name && result.breaking && result.breaking.length > 0) {
                          sensitivity = result.breaking[0].sensitivity || '-';
                          break;
                        }
                      }
                      return (
                        <span className={sensitivity === 'R' ? 'lab-report-resistant-text' : sensitivity === 'S' ? 'lab-report-sensitive-text' : ''}>
                          {sensitivity}
                        </span>
                      );
                    })()}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    ));
  }
  return null;
})()}
        </div>
      </div>
    
    </div>
  );
};
 
export default LabReportInterface;
