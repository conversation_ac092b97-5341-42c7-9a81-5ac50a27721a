import { useState, useEffect } from 'react';
import NewSample from './NewSample.js';

function Sample({ onNavigateToReview }) {
  const [samples, setSamples] = useState([]);
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [filterText, setFilterText] = useState('');
  const [showImportModal, setShowImportModal] = useState(false);
  const [showNewSampleModal, setShowNewSampleModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedSample, setSelectedSample] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [editFormData, setEditFormData] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API配置 - 从环境变量获取
  const API_BASE = process.env.REACT_APP_BASE_URL;

  // 从patientSample集合加载样品数据
  const loadSamples = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE}api/samples`);
      const result = await response.json();
      
      if (result.success && result.data) {
        setSamples(result.data);
      } else {
          setSamples([]);
      }
    } catch (error) {
       setSamples([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取样本详细信息
  const fetchSampleDetail = async (specimenNumber) => {
    try {
      console.log('🔍 获取样本详情:', specimenNumber);
      const apiUrl = `${API_BASE}api/patients/${specimenNumber}`;
      console.log('🔍 完整API URL:', apiUrl);

      const response = await fetch(apiUrl);
      console.log('🔍 Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🔍 详情API响应:', result);

      // API返回格式是 { data: patient }
      if (result && result.data) {
        console.log('✅ 设置样本数据:', result.data);
        setSelectedSample(result.data);

        // 设置编辑表单数据
        setEditFormData({
          labId: result.data.specimenNumber || '',
          bacteriaId: result.data.bacteriaId || '', // 新增bacteriaId
          bacteriaName: result.data.cultureResult || '',
          sampleSource: result.data.sampleType || '',
          patientId: result.data.patient_id || '',
          patientName: result.data.name || '',
          age: result.data.age || '',
          gender: result.data.gender || '男',
          department: result.data.department || '',
          bed: result.data.bed || '',
          ward: result.data.ward || '',
          medicalRecordNumber: result.data.medicalRecordNumber || '',
          smearResult: result.data.smearResult || '',
          remark: result.data.remark || '',
          testDataList: result.data.testDataList || []
        });

        setIsEditing(true); // 默认进入编辑模式
        setShowDetailModal(true);
        console.log('✅ 模态框应该显示了');
      } else {
        console.warn('❌ API响应中没有data字段:', result);
        alert('未找到样本详细信息');
      }
    } catch (error) {
      console.error('❌ 获取样本详情失败:', error);
      alert(`获取样本详细信息失败: ${error.message}`);
    }
  };

  // 处理Lab ID点击
  const handleLabIdClick = (e, labId) => {
    e.stopPropagation(); // 阻止事件冒泡到行点击
    console.log('🖱️ Lab ID被点击:', labId);
    console.log('🔧 API_BASE:', API_BASE);
    fetchSampleDetail(labId);
  };

  // 处理编辑表单输入变化
  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 处理testDataList的变化
  const handleTestDataChange = (index, field, value) => {
    setEditFormData(prev => ({
      ...prev,
      testDataList: prev.testDataList.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  // 添加新的测试数据
  const addTestData = () => {
    setEditFormData(prev => ({
      ...prev,
      testDataList: [...prev.testDataList, {
        testMethod: '',
        testResult: '',
        testSupplement: '',
        testSupplementResult: ''
      }]
    }));
  };

  // 删除测试数据
  const removeTestData = (index) => {
    setEditFormData(prev => ({
      ...prev,
      testDataList: prev.testDataList.filter((_, i) => i !== index)
    }));
  };

  // 保存编辑的数据
  const handleSaveEdit = async () => {
    try {
      setSaveLoading(true);

      // 转换表单数据为Patient模型格式
      const patientData = {
        patient_id: editFormData.patientId,
        specimenNumber: editFormData.labId,
        name: editFormData.patientName,
        age: editFormData.age,
        gender: editFormData.gender,
        department: editFormData.department,
        sampleType: editFormData.sampleSource,
        cultureResult: editFormData.bacteriaName,
        bacteriaId: editFormData.bacteriaCode, // 确保bacteriaId被保存
        bed: editFormData.bed,
        ward: editFormData.ward,
        medicalRecordNumber: editFormData.medicalRecordNumber,
        smearResult: editFormData.smearResult,
        remark: editFormData.remark,
        hospital_code: selectedSample.hospital_code || '123456',
        consumableboardNumber: selectedSample.consumableboardNumber || Math.floor(Math.random() * 9999).toString(),
        sampleCode: editFormData.sampleSource === '血液' ? 'bl' :
                    editFormData.sampleSource === '尿液' ? 'ur' :
                    editFormData.sampleSource === '脑脊液' ? 'csf' :
                    editFormData.sampleSource === 'Blood' ? 'bl' :
                    editFormData.sampleSource === 'Urine' ? 'ur' :
                    editFormData.sampleSource === 'CSF' ? 'csf' : 'ot',
        testDataList: editFormData.testDataList
      };

      console.log('🔧 保存患者数据:', patientData);

      const response = await fetch(`http://localhost:5000/patients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(patientData)
      });

      const result = await response.json();
      console.log('🔧 保存API响应:', result);

      if (response.ok && result.message === '保存成功') {
        alert('样本信息保存成功！');
        setShowDetailModal(false);
        loadSamples(); // 重新加载列表
      } else {
        alert(`保存失败: ${result.error || result.message}`);
      }
    } catch (error) {
      console.error('❌ 保存样本失败:', error);
      alert(`保存样本失败: ${error.message}`);
    } finally {
      setSaveLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    loadSamples();
  }, []);

  // 调试模态框状态
  useEffect(() => {
    console.log('🔍 模态框状态变化:', { showDetailModal, selectedSample: !!selectedSample });
  }, [showDetailModal, selectedSample]);


  // 排序功能
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // 行选择功能
  const handleRowSelect = (id) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(id)) {
      newSelected.delete(id);
      setSelectedRows(newSelected);
    } else {
      newSelected.add(id);
      setSelectedRows(newSelected);

      // 找到选中的样品并跳转到Mic Control页面
      const selectedSample = samples.find(sample => (sample._id || sample.id) === id);
      if (selectedSample && onNavigateToReview) {
        onNavigateToReview(selectedSample.labId);
      }
    }
  };

  // 全选功能
  const handleSelectAll = () => {
    if (selectedRows.size === filteredSamples.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(filteredSamples.map(sample => sample.id)));
    }
  };

  // 过滤和排序数据
  const filteredSamples = samples
    .filter(sample => 
      Object.values(sample).some(value => 
        value.toString().toLowerCase().includes(filterText.toLowerCase())
      )
    )
    .sort((a, b) => {
      if (!sortConfig.key) return 0;
      
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });

  // 状态颜色
  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'text-green-600 bg-green-50';
      case 'Processing': return 'text-blue-600 bg-blue-50';
      case 'Pending': return 'text-orange-600 bg-orange-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // 优先级颜色
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return 'text-red-600 bg-red-50';
      case 'High': return 'text-orange-600 bg-orange-50';
      case 'Normal': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // 处理新增样品
  const handleNewSample = () => {
    setShowNewSampleModal(true);
  };

  // 处理样品添加成功
  const handleSampleAdded = () => {
    loadSamples(); // 重新加载样品列表
  };

  // 处理刷新
  const handleRefresh = () => {
    loadSamples();
  };

  // 处理文件导入
  const handleImport = () => {
    setShowImportModal(true);
  };

  // 处理文件选择
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log('Selected file:', file.name);
      // TODO: 实现文件导入逻辑
    }
  };

  // 删除样本
  const handleDeleteSample = async (sample) => {
    if (!window.confirm(`确定要删除样本 ${sample.labId} (患者: ${sample.patientName}) 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      console.log('🗑️ 删除样本:', sample);

      // 使用patient_id删除患者记录
      const response = await fetch(`http://localhost:5000/api/patients/${sample.patientId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ 删除成功:', result);
        alert('样本删除成功！');
        loadSamples(); // 重新加载列表
      } else {
        const error = await response.json();
        console.error('❌ 删除失败:', error);
        alert(`删除失败: ${error.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('❌ 删除样本失败:', error);
      alert(`删除样本失败: ${error.message}`);
    }
  };


  return (
    <div className="h-full bg-white flex flex-col">
      {/* 工具栏 */}
      <div className="border-b border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Sample Management</h1>
          <div className="flex space-x-2">
            <button
              onClick={handleNewSample}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              + New Sample
            </button>
            <button
              onClick={handleImport}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              📥 Import
            </button>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
              📤 Export
            </button>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              🔄 {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>
        
        {/* 搜索和过滤 */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search samples... (Specimen ID, Patient ID, Bacteria, etc.)"
              value={filterText}
              onChange={(e) => setFilterText(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="text-sm text-gray-600">
            {selectedRows.size > 0 && `${selectedRows.size} selected | `}
            {filteredSamples.length} of {samples.length} samples
            <span className="ml-4 text-blue-600 font-medium">
              💡 Tip: Select a checkbox to view results
            </span>
          </div>
        </div>
      </div>

      {/* DataGridView 样式表格 */}
      <div className="flex-1 overflow-auto">
        <table className="w-full border-collapse bg-white">
          <thead className="bg-gray-100 border-b-2 border-gray-300 sticky top-0">
            <tr>
              <th className="w-12 p-2 border-r border-gray-300">
                <input
                  type="checkbox"
                  checked={selectedRows.size === filteredSamples.length && filteredSamples.length > 0}
                  onChange={handleSelectAll}
                  className="w-4 h-4"
                />
              </th>
              {[
                { key: 'labId', label: 'Specimen ID' },
                { key: 'bacteriaName', label: 'Bacteria Name' },
                { key: 'sampleSource', label: 'Sample Source' },
                { key: 'patientId', label: 'Patient ID' },
                { key: 'collectionDate', label: 'Collection Date' },
                { key: 'status', label: 'Status' },
                { key: 'priority', label: 'Priority' },
                { key: 'department', label: 'Department' }
              ].map(({ key, label }) => (
                <th
                  key={key}
                  className="p-3 text-left font-semibold text-gray-700 border-r border-gray-300 cursor-pointer hover:bg-gray-200 select-none"
                  onClick={() => handleSort(key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{label}</span>
                    {sortConfig.key === key && (
                      <span className="text-blue-600">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              <th className="p-3 text-left font-semibold text-gray-700 border-r border-gray-300">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredSamples.map((sample, index) => (
              <tr
                key={sample._id || sample.id}
                className={`border-b border-gray-200 hover:bg-blue-50 cursor-pointer ${
                  selectedRows.has(sample._id || sample.id) ? 'bg-blue-100' : index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                }`}
                onClick={() => handleRowSelect(sample._id || sample.id)}
              >
                <td className="p-2 border-r border-gray-300 text-center">
                  <input
                    type="checkbox"
                    checked={selectedRows.has(sample._id || sample.id)}
                    onChange={() => handleRowSelect(sample._id || sample.id)}
                    className="w-4 h-4"
                  />
                </td>
                <td className="p-3 border-r border-gray-300 font-mono font-semibold">
                  <button
                    onClick={(e) => handleLabIdClick(e, sample.labId)}
                    className="text-blue-700 hover:text-blue-900 hover:underline cursor-pointer transition-colors"
                    title="点击查看详细信息"
                  >
                    {sample.labId}
                  </button>
                </td>
                <td className="p-3 border-r border-gray-300 font-medium">
                  {sample.bacteriaName}
                </td>
                <td className="p-3 border-r border-gray-300">
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700">
                    {sample.sampleSource}
                  </span>
                </td>
                <td className="p-3 border-r border-gray-300 font-mono">
                  {sample.patientId}
                </td>
                <td className="p-3 border-r border-gray-300">
                  {sample.collectionDate}
                </td>
                <td className="p-3 border-r border-gray-300">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sample.status)}`}>
                    {sample.status}
                  </span>
                </td>
                <td className="p-3 border-r border-gray-300">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(sample.priority)}`}>
                    {sample.priority}
                  </span>
                </td>
                <td className="p-3 border-r border-gray-300">
                  {sample.department}
                </td>
                <td className="p-3">
                  <div className="flex space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // 阻止行点击事件
                        handleLabIdClick(e, sample.labId);
                      }}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                      title="查看详情"
                    >
                      详情
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // 阻止行点击事件
                        handleDeleteSample(sample);
                      }}
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                      title="删除样本"
                    >
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 状态栏 */}
      <div className="border-t border-gray-300 bg-gray-100 px-4 py-2 text-sm text-gray-600">
        <div className="flex items-center justify-between">
          <div>Ready | Total: {samples.length} samples</div>
          <div>bioMérieux AES™ - Sample Management System</div>
        </div>
      </div>

      {/* Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl p-6 w-96 max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Import Samples</h3>
              <button
                onClick={() => setShowImportModal(false)}
                className="text-gray-400 hover:text-gray-600 text-xl"
              >
                ×
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-600 mb-4">
                Select a file to import sample data. Supported formats: CSV, Excel (.xlsx)
              </p>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                <div className="mb-4">
                  <span className="text-4xl">📁</span>
                </div>
                <p className="text-gray-600 mb-2">Click to select file or drag and drop</p>
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-input"
                />
                <label
                  htmlFor="file-input"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg cursor-pointer inline-block transition-colors"
                >
                  Choose File
                </label>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowImportModal(false)}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Cancel
              </button>
              <button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Import
              </button>
            </div>
          </div>
        </div>
      )}

      {/* New Sample Modal */}
      {showNewSampleModal && (
        <NewSample
          onClose={() => setShowNewSampleModal(false)}
          onSampleAdded={handleSampleAdded}
        />
      )}

      {/* Sample Detail Modal */}
      {showDetailModal && selectedSample && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl p-6 w-4/5 max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">
                样本详细信息 - {selectedSample.specimenNumber || selectedSample.patient_id}
              </h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
              >
                ×
              </button>
            </div>

            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 基本信息 */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-gray-800 mb-3">基本信息</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">患者ID *</label>
                      <input
                        type="text"
                        name="patientId"
                        value={editFormData.patientId || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">患者姓名 *</label>
                      <input
                        type="text"
                        name="patientName"
                        value={editFormData.patientName || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">性别</label>
                      <select
                        name="gender"
                        value={editFormData.gender || '男'}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="男">男</option>
                        <option value="女">女</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">年龄</label>
                      <input
                        type="number"
                        name="age"
                        value={editFormData.age || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">科室 *</label>
                      <input
                        type="text"
                        name="department"
                        value={editFormData.department || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">病房</label>
                      <input
                        type="text"
                        name="ward"
                        value={editFormData.ward || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">床位</label>
                      <input
                        type="text"
                        name="bed"
                        value={editFormData.bed || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">病历号</label>
                      <input
                        type="text"
                        name="medicalRecordNumber"
                        value={editFormData.medicalRecordNumber || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* 样本信息 */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-lg font-semibold text-gray-800 mb-3">样本信息</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">标本编号 *</label>
                      <input
                        type="text"
                        name="labId"
                        value={editFormData.labId || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">样本类型 *</label>
                      <select
                        name="sampleSource"
                        value={editFormData.sampleSource || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">选择样本类型</option>
                        <option value="Urine">Urine</option>
                        <option value="Blood">Blood</option>
                        <option value="CSF">CSF</option>
                        <option value="Sputum">Sputum</option>
                        <option value="Wound">Wound</option>
                        <option value="血液">血液</option>
                        <option value="尿液">尿液</option>
                        <option value="脑脊液">脑脊液</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">培养结果 *</label>
                      <input
                        type="text"
                        name="bacteriaName"
                        value={editFormData.bacteriaName || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    {/* Bacteria ID */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">菌株ID</label>
                      <input
                        type="text"
                        name="bacteriaId"
                        value={editFormData.bacteriaId || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入菌株ID (可选)"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">涂片结果</label>
                      <input
                        type="text"
                        name="smearResult"
                        value={editFormData.smearResult || ''}
                        onChange={handleEditInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">备注</label>
                      <textarea
                        name="remark"
                        value={editFormData.remark || ''}
                        onChange={handleEditInputChange}
                        rows="3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* 检测数据列表 */}
              <div className="col-span-1 md:col-span-2 mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-800">检测数据列表</h4>
                  <button
                    type="button"
                    onClick={addTestData}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
                  >
                    <span className="mr-2">+</span>
                    添加检测数据
                  </button>
                </div>

                {editFormData.testDataList && editFormData.testDataList.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                    <p>暂无检测数据</p>
                    <p className="text-sm mt-1">点击"添加检测数据"按钮开始添加</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {editFormData.testDataList && editFormData.testDataList.map((testData, index) => (
                      <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-medium text-gray-700">检测数据 #{index + 1}</h5>
                          <button
                            type="button"
                            onClick={() => removeTestData(index)}
                            className="text-red-600 hover:text-red-800 font-medium"
                          >
                            删除
                          </button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">检测方法</label>
                            <input
                              type="text"
                              value={testData.testMethod || ''}
                              onChange={(e) => handleTestDataChange(index, 'testMethod', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="输入检测方法"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">检测结果</label>
                            <input
                              type="text"
                              value={testData.testResult || ''}
                              onChange={(e) => handleTestDataChange(index, 'testResult', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="输入检测结果"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">补充信息</label>
                            <input
                              type="text"
                              value={testData.testSupplement || ''}
                              onChange={(e) => handleTestDataChange(index, 'testSupplement', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="输入补充信息"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">补充结果</label>
                            <input
                              type="text"
                              value={testData.testSupplementResult || ''}
                              onChange={(e) => handleTestDataChange(index, 'testSupplementResult', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="输入补充结果"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </form>

            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowDetailModal(false)}
                className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSaveEdit}
                disabled={saveLoading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center"
              >
                {saveLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    保存中...
                  </>
                ) : (
                  '保存'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Sample;
