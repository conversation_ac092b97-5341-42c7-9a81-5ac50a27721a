import React, { useState, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import { Modal } from 'antd';

const PhenotypeChartModal = ({ open, onClose, antimicrobialAgents = [], micDistributionData = [] }) => {
  const [selectedOrganism, setSelectedOrganism] = useState('Klebsiella pneumoniae ssp pneumoniae');
  const [selectedSpecies, setSelectedSpecies] = useState('Klebsiella pneumoniae ssp pneumoniae');
  const [selectedFamily, setSelectedFamily] = useState('BETA-LACTAMS');
 
  const chartRefs = useRef([]);
  const chartInstances = useRef([]);

  const groups = [
    'WILD PENICILLINASE',
    'CARBAPENEMASE (+ OR - ESβL)', 
    'IMPERMEABILITY CAPsA (CEPH = +/- ESβL)',
    'ACQ PAsE + IMPERMEABILITY (CEPH+IMTACS)',
    'ACQUIRED CEPHALOSPORINASE+EXCEPT ACC-1',
    'ACQUIRED β-LACTAMASE'
  ];

  // Generate sample data for each antibiotic and group
  const generateChartData = () => {
    return antimicrobialAgents.map(() => 
      groups.map(() => Math.random() * 100)
    );
  };

  const [chartData] = useState(generateChartData());

  // Helper to generate a random frequency breakdown for a bar
  const generateRandomBarSegments = () => {
    let remaining = 100;
    const getRandom = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
    const royal = getRandom(31, Math.min(60, remaining));
    remaining -= royal;
    const dodger = getRandom(21, Math.min(30, remaining));
    remaining -= dodger;
    const sky = getRandom(11, Math.min(20, remaining));
    remaining -= sky;
    const powder = getRandom(1, Math.min(10, remaining));
    remaining -= powder;
    const white = Math.max(0, remaining);
    return [
      { color: '#4169E1', value: royal },
      { color: '#1E90FF', value: dodger },
      { color: '#87CEEB', value: sky },
      { color: '#B0E0E6', value: powder },
      { color: '#ffffff', value: white }
    ];
  };

  // 动态分段百分比
  const getDynamicSegments = (rowIndex, colIndex) => {
    const base = (rowIndex * groups.length + colIndex) % 5;
    const royal = 40 + (base === 0 ? 10 : 0);
    const dodger = 25 + (base === 1 ? 5 : 0);
    const sky = 15 + (base === 2 ? 5 : 0);
    const powder = 8 + (base === 3 ? 2 : 0);
    let sum = royal + dodger + sky + powder;
    const white = Math.max(0, 100 - sum);
    return [
      { color: '#4169E1', value: royal },
      { color: '#1E90FF', value: dodger },
      { color: '#87CEEB', value: sky },
      { color: '#B0E0E6', value: powder },
      { color: '#ffffff', value: white }
    ];
  };

  // 粉色箭头和方框
  const getPinkArrowBox = (rowIndex, colIndex) => {
    const showArrow = Math.random() < 0.2;
    const showBox = Math.random() < 0.1;
    return { showArrow, showBox };
  };

  // 蓝色梯度色板
const blueGradient = [
  '#B0E0E6', // powder blue (最浅)
  '#87CEEB', // sky blue
  '#1E90FF', // dodger blue
  '#4169E1', // royal blue (最深)
];

// 获取每个抗生素-机制的分布数据和MIC箭头信息
const getDistributionSegments = (agentName, groupName, currentColIndex = null, hitColIndex = null) => {
  // 调试：始终输出agentName、groupName、dist、micDistributionData长度
  let dist = undefined;
  if (micDistributionData && Array.isArray(micDistributionData)) {
    dist = micDistributionData.find(d => {
      if (!d.displayName || !agentName) return false;
      try {
        const regex = new RegExp(agentName, 'i');
        return regex.test(d.displayName);
      } catch {
        return false;
      }
    });
  }

  if (!dist || !dist.distribution || !Array.isArray(dist.distribution)) return { segments: [], min: null, max: null, mechanism: null, micValue: null, showArrow: false, arrowDir: null };
  const mechanism = dist.mechanism || '';
  // --- Removed groupName/mechanism checks here ---
  const sorted = [...dist.distribution].sort((a, b) => (a.min ?? a.mic ?? 0) - (b.min ?? b.mic ?? 0));
  // Support both min/max or mic fields
  const min = sorted[0]?.min ?? sorted[0]?.mic;
  const max = sorted[sorted.length - 1]?.max ?? sorted[sorted.length - 1]?.mic;

  // 从antimicrobialAgents找micValue，优先用机制匹配，否则回退仅name
  let micValue = null;
  if (Array.isArray(antimicrobialAgents)) {
    let agent = antimicrobialAgents.find(a =>
      a.name === agentName &&
      ((a.mechanism && a.mechanism === groupName) || (a.groupName && a.groupName === groupName))
    );
    // 如果没有机制字段匹配，回退只用name匹配
    if (!agent) {
      agent = antimicrobialAgents.find(a => a.name === agentName);
    }
    if (
      agent &&
      agent.micValue !== undefined &&
      agent.micValue !== null &&
      agent.micValue !== '' &&
      !isNaN(Number(agent.micValue))
    ) {
      micValue = Number(agent.micValue);
    }
  }
  
  // 调试输出
  // if (agentName.includes('亚胺培南') || (dist.displayName && dist.displayName.includes('亚胺培南'))) {
  //   console.log('[DEBUG][PhenotypeChartModal] 亚胺培南:', {min, max, micValue, segments: sorted, agentName, groupName, dist});
  // }
  // 箭头逻辑
  let showArrow = false, arrowDir = null, hitMechanism = false;
  if (micValue !== null && min !== null && max !== null) {
    if (micValue > max) {
      showArrow = true; arrowDir = 'left';
      // 只有命中的机制才显示箭头
      if (currentColIndex !== null && hitColIndex !== null && currentColIndex === hitColIndex) {
        hitMechanism = true;
      }
    } else if (micValue < min) {
      showArrow = true; arrowDir = 'right';
      if (currentColIndex !== null && hitColIndex !== null && currentColIndex === hitColIndex) {
        hitMechanism = true;
      }
    }
  }
  // 返回分布段和箭头信息
  return { segments: sorted, min, max, mechanism, micValue, showArrow, arrowDir, hitMechanism };
};

  useEffect(() => {
    if (!open) return;
    antimicrobialAgents.forEach((item, rowIndex) => {
      groups.forEach((group, colIndex) => {
        const idx = rowIndex * groups.length + colIndex;
        const canvas = chartRefs.current[idx];
        if (chartInstances.current[idx]) {
          chartInstances.current[idx].destroy();
          chartInstances.current[idx] = null;
        }
        if (canvas) {
          // 优先用真实分布数据，否则用动态分段
          let { segments, min, max, mechanism, micValue, showArrow, arrowDir, hitMechanism } = getDistributionSegments(item.name, group);
          // 计算bar宽度百分比（max-min/16，16为常见MIC范围，最小10%，最大100%）
          let barWidthPercent = 100;
          if (micDistributionData && Array.isArray(micDistributionData)) {
            const dist = micDistributionData.find(d => {
              if (!d.displayName || !item.name) return false;
              try {
                const regex = new RegExp(item.name, 'i');
                return regex.test(d.displayName);
              } catch {
                return false;
              }
            });
            if (dist && dist.distribution && dist.distribution.length > 0) {
              const sorted = [...dist.distribution].sort((a, b) => (a.min ?? 0) - (b.min ?? 0));
              const min = sorted[0]?.min ?? 0;
              const max = sorted[sorted.length - 1]?.max ?? 16;
              barWidthPercent = Math.max(10, Math.min(100, ((max - min) / 16) * 100));
            }
          }
          if (!segments || segments.length === 0) {
            segments = getDynamicSegments(rowIndex, colIndex);
          }
          const total = segments.reduce((sum, seg) => sum + seg.value, 0) || 1;
          // 修复bar tooltip显示Frequency而不是min-max的问题
          const barLabel = (min !== undefined && max !== undefined && min !== null && max !== null) ? `${min} - ${max}` : '';
          chartInstances.current[idx] = new Chart(canvas, {
            type: 'bar',
            data: {
              labels: [barLabel],
              datasets: [
                {
                  data: [100],
                  backgroundColor: function(context) {
                    const chart = context.chart;
                    const {ctx, chartArea} = chart;
                    if (!chartArea) return '#fff';
                    // Symmetric gradient: white → light blue → deep blue → light blue → white
                    const gradient = ctx.createLinearGradient(chartArea.left, 0, chartArea.right, 0);
                    gradient.addColorStop(0.00, '#ffffff'); // white
                    gradient.addColorStop(0.20, '#b0e0e6'); // powder blue (light blue)
                    gradient.addColorStop(0.50, '#1e3a8a'); // deep blue
                    gradient.addColorStop(0.80, '#b0e0e6'); // powder blue (light blue)
                    gradient.addColorStop(1.00, '#ffffff'); // white
                    return gradient;
                  },
                  borderWidth: 0,
                  borderColor: 'rgba(0,0,0,0)',
                  barPercentage: barWidthPercent / 100, // 动态宽度
                  categoryPercentage: 1.0,
                  label: barLabel,
                }
              ]
            },
            options: {
              indexAxis: 'y',
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: { display: false },
                tooltip: {
                  enabled: true,
                  callbacks: {
                    label: function(context) {
                      // 直接显示min-max
                      return context.chart.data.labels[context.dataIndex] || '';
                    }
                  }
                }
              },
              scales: {
                x: { display: false, beginAtZero: true, max: 100, stacked: false },
                y: { display: false, stacked: false }
              },
              layout: { padding: 0 },
              elements: { bar: { borderWidth: 0 } }
            }
          });
          // 挂载min/max到chart实例，供tooltip使用
          chartInstances.current[idx].$customRange = { min, max };
        }
      });
    });
    return () => {
      chartInstances.current.forEach(chart => {
        if (chart) chart.destroy();
      });
      chartInstances.current = [];
    };
  }, [antimicrobialAgents, groups, open, micDistributionData]);

  if (!open) return null;

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      title="表型分布图"
      width={900}
      style={{ top: 40 }}
      bodyStyle={{ padding: 0, maxHeight: '70vh', overflow: 'auto' }}
      destroyOnClose
    >
      {/* 新增：三个下拉菜单 */}
      <div style={{ display: 'flex', gap: 16, alignItems: 'center', margin: '10px 0 10px 10px' }}>
        <div>
          <label style={{ fontSize: 12, color: '#333', marginRight: 4 }}>菌种：</label>
          <select
            value={selectedOrganism}
            onChange={e => setSelectedOrganism(e.target.value)}
            style={{ minWidth: 180, height: 28 }}
          >
            {/* 实际项目可根据 props 或数据源动态生成 */}
            <option value="Klebsiella pneumoniae ssp pneumoniae">Klebsiella pneumoniae ssp pneumoniae</option>
            <option value="Escherichia coli">Escherichia coli</option>
            <option value="Pseudomonas aeruginosa">Pseudomonas aeruginosa</option>
          </select>
        </div>
        <div>
          <label style={{ fontSize: 12, color: '#333', marginRight: 4 }}>表型：</label>
          <select
            value={selectedSpecies}
            onChange={e => setSelectedSpecies(e.target.value)}
            style={{ minWidth: 180, height: 28 }}
          >
            <option value="Klebsiella pneumoniae ssp pneumoniae">Klebsiella pneumoniae ssp pneumoniae</option>
            <option value="Escherichia coli">Escherichia coli</option>
            <option value="Pseudomonas aeruginosa">Pseudomonas aeruginosa</option>
          </select>
        </div>
        <div>
          <label style={{ fontSize: 12, color: '#333', marginRight: 4 }}>药物种类：</label>
          <select
            value={selectedFamily}
            onChange={e => setSelectedFamily(e.target.value)}
            style={{ minWidth: 180, height: 28 }}
          >
            <option value="BETA-LACTAMS">BETA-LACTAMS</option>
            <option value="AMINOGLYCOSIDES">AMINOGLYCOSIDES</option>
            <option value="QUINOLONES">QUINOLONES</option>
          </select>
        </div>
      </div>
      <div style={{
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#c0c0c0',
        padding: '10px',
        minHeight: '200px',
        maxHeight: '60vh',
        overflow: 'auto',
      }}>
        {/* Chart Header */}
        <div style={{ 
          display: 'grid',
          gridTemplateColumns: '200px repeat(6, 150px)', // 机制列宽增大到150px
          gap: '1px',
          marginBottom: '5px'
        }}>
          <div></div>
          {groups.map((group, index) => (
            <div key={index} style={{
              backgroundColor: '#d3d3d3',
              padding: '5px 2px',
              textAlign: 'center',
              fontSize: '9px',
              fontWeight: 'bold',
              border: '1px solid #999',
              minHeight: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#000',
              width: 150, // 明确指定宽度
              boxSizing: 'border-box',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'normal',
              wordBreak: 'break-all',
            }}>
              {group}
            </div>
          ))}
        </div>
        {/* Chart Data with Bar Charts */}
        <div style={{ backgroundColor: '#ffffff', border: '1px solid #ccc' }}>
          {antimicrobialAgents.map((item, rowIndex) => (
            <div key={rowIndex} style={{
              display: 'grid',
              gridTemplateColumns: '200px repeat(6, 150px)', // 数据区同样加宽
              gap: '1px',
              borderBottom: '1px solid #e0e0e0',
              minHeight: '25px'
            }}>
              {/* 只渲染名称，不渲染箭头和方框 */}
              <div style={{
                padding: '5px',
                backgroundColor: '#f8f8f8',
                fontSize: '10px',
                fontWeight: 'bold',
                border: '1px solid #e0e0e0',
                display: 'flex',
                alignItems: 'center',
                color: '#000'
              }}>
                {item.name}
              </div>
              {/* 机制列才渲染箭头和方框 */}
              {groups.map((group, colIndex) => {
                // 传递当前 colIndex 和命中 colIndex
                const distInfo = getDistributionSegments(item.name, group, colIndex, null);
                const { segments, min, max, mechanism, micValue, showArrow, arrowDir, hitMechanism } = distInfo;
                // 计算命中机制的 colIndex（必须在 micValue 已定义后）
                let hitColIndex = null;
                if (micValue !== null && min !== null && max !== null) {
                  if (micValue > max || micValue < min) {
                    hitColIndex = colIndex;
                  }
                }
                // 重新获取 distInfo，带上 hitColIndex
                const finalDistInfo = getDistributionSegments(item.name, group, colIndex, hitColIndex);
                const { showArrow: finalShowArrow, arrowDir: finalArrowDir, hitMechanism: finalHitMechanism, segments: finalSegments, min: finalMin, max: finalMax, micValue: finalMicValue, mechanism: finalMechanism } = finalDistInfo;
                // 算法：定位MIC在bar内的百分比
                let boxPercent = null, closestIdx = 0, boxLeft = '50%';
                if (finalMicValue !== null && finalSegments.length > 0 && finalMicValue >= finalMin && finalMicValue <= finalMax) {
                  let minDiff = Infinity;
                  for (let i = 0; i < finalSegments.length; i++) {
                    const segMin = finalSegments[i].min ?? 0;
                    const segMax = finalSegments[i].max ?? 0;
                    if (finalMicValue >= segMin && finalMicValue <= segMax) {
                      closestIdx = i;
                      const segRange = segMax - segMin;
                      let segPercent = 0.5;
                      if (segRange > 0) {
                        segPercent = (finalMicValue - segMin) / segRange;
                      }
                      let accPercent = 0;
                      for (let j = 0; j < i; j++) {
                        accPercent += finalSegments[j].percent;
                      }
                      const thisSegPercent = finalSegments[i].percent ?? 0;
                      // 算法：本机制内的百分比
                      const percentInBar = (accPercent + thisSegPercent * segPercent);
                      // 算法：bar内的像素位置
                      boxLeft = `calc(50% + ${percentInBar * 100}% )`;
                      break;
                    }
                    // 兼容老逻辑：找最近区间
                    const segMid = (segMin + segMax) / 2;
                    const diff = Math.abs(finalMicValue - segMid);
                    if (diff < minDiff) {
                      minDiff = diff;
                      closestIdx = i;
                    }
                  }
                  boxPercent = finalSegments[closestIdx]?.percent;
                }
                // 4. 判断方框和箭头
                let showBox = false;
                if (finalMicValue !== null && finalMin !== null && finalMax !== null) {
                  if (finalMicValue < finalMin || finalMicValue > finalMax) {
                    showBox = false;
                    // showArrow/arrowDir 已由getDistributionSegments返回
                  } else {
                    showBox = true;
                    // boxLeft 已动态计算
                  }
                }
                // 箭头定位到白色区域
                let arrowInWhite = false;
                let arrowLeft = '50%';
                let leftArrowCustom = null;
                if (finalShowArrow && finalHitMechanism && finalSegments && finalSegments.length > 0) {
                  // 找到白色段
                  let accPercent = 0;
                  for (let i = 0; i < finalSegments.length; i++) {
                    const color = finalSegments[i].color;
                    if (color && (color === '#ffffff' || color.toLowerCase() === 'white')) {
                      const whitePercent = finalSegments[i].percent ?? (finalSegments[i].value / 100);
                      // 箭头放在白色区中心
                      arrowLeft = `${(accPercent + whitePercent / 2) * 100}%`;
                      arrowInWhite = true;
                      break;
                    }
                    accPercent += finalSegments[i].percent ?? (finalSegments[i].value / 100);
                  }
                }
                return (
                  <div key={colIndex} style={{
                    backgroundColor: '#ffffff',
                    border: '1px solid #e0e0e0',
                    position: 'relative',
                    padding: '2px',
                    color: '#000'
                  }}>
                    <canvas
                      ref={el => {
                        chartRefs.current[rowIndex * groups.length + colIndex] = el;
                      }}
                      style={{
                        width: '100%',
                        height: '20px'
                      }}
                    />
                    {/* 粉色方框和箭头逻辑 */}
                    {showBox && (
                      <div style={{
                        position: 'absolute',
                        left: boxLeft,
                        top: '50%',
                        width: '8px',
                        height: '8px',
                        backgroundColor: '#ff1493',
                        borderRadius: '3px',
                        zIndex: 2,
                        transform: 'translate(-50%, -50%)',
                      }}></div>
                    )}
                    {/* 箭头画在白色区域内 */}
                    {finalShowArrow && finalHitMechanism && arrowInWhite && (
                      <div style={{
                        position: 'absolute',
                        left: arrowLeft,
                        top: '50%',
                        color: '#ff1493',
                        fontSize: '8px',
                        fontWeight: 'bold',
                        zIndex: 2,
                        display: 'flex',
                        alignItems: 'center',
                        height: '8px',
                        pointerEvents: 'none',
                        transform: finalArrowDir === 'left' ? 'translate(-50%, -50%) scaleX(-1)' : 'translate(-50%, -50%)',
                      }}>
                        <span style={{
                          display: 'inline-block',
                          lineHeight: '8px',
                          verticalAlign: 'middle',
                        }}>▶</span>
                      </div>
                    )}
                    {/* 兼容老逻辑：如果没有白色段则继续原箭头逻辑 */}
                    {finalShowArrow && finalHitMechanism && !arrowInWhite && finalArrowDir === 'right' && (
                      <div style={{
                        position: 'absolute',
                        right: '-12px',
                        top: '50%',
                        color: '#ff1493',
                        fontSize: '8px',
                        fontWeight: 'bold',
                        zIndex: 2,
                        display: 'flex',
                        alignItems: 'center',
                        height: '8px',
                        pointerEvents: 'none',
                        transform: 'translateY(-50%)', // 垂直居中
                      }}>
                        <span style={{
                          display: 'inline-block',
                          lineHeight: '8px',
                          verticalAlign: 'middle',
                        }}>▶</span>
                      </div>
                    )}
                   {finalShowArrow && finalHitMechanism && !arrowInWhite && finalArrowDir === 'left' && (
              <div
                style={{
                  position: 'absolute',
                  left: '-12px', // ✅ 修复：将箭头放在 bar 的左侧外面
                  top: '50%',
                  color: '#ff1493',
                  fontSize: '8px',
                  fontWeight: 'bold',
                  zIndex: 2,
                  display: 'flex',
                  alignItems: 'center',
                  height: '8px',
                  pointerEvents: 'none',
                  transform: 'translateY(-50%) scaleX(-1)' // ✅ 向左反转
                }}
              >
                <span
                  style={{
                    display: 'inline-block',
                    lineHeight: '8px',
                    verticalAlign: 'middle'
                  }}
                >
                  ▶
                </span>
              </div>
            )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default PhenotypeChartModal;
