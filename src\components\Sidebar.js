// import React from 'react'; // Not needed in React 17+

function Sidebar({ currentPage, setCurrentPage }) {
  const menuItems = [
    { name: 'Dashboard', icon: '📊', badge: null },
    { name: 'Isolate Control', icon: '🧪', badge: '247' },
    { name: 'Mic Control', icon: '📋', badge: '18' },
    { name: 'Quality Control', icon: '✅', badge: null },
    { name: 'Reports', icon: '📄', badge: null },
    { name: 'Setting<PERSON>', icon: '⚙️', badge: '3' },
  ];

  const handleMenuClick = (itemName) => {
    setCurrentPage(itemName);
  };

  return (
    <aside className="w-72 h-full bg-white shadow-xl border-r border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800">Navigation</h2>
        <p className="text-sm text-gray-600 mt-1">Laboratory Management</p>
      </div>

      {/* Navigation Menu */}
      <nav className="p-4 flex-1">
        <ul className="space-y-2">
          {menuItems.map((item, index) => (
            <li key={index}>
              <button
                onClick={() => handleMenuClick(item.name)}
                className={`w-full flex items-center justify-between px-4 py-3 rounded-xl text-left transition-all duration-200 group ${
                  currentPage === item.name
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105'
                    : 'text-gray-700 hover:bg-gray-50 hover:shadow-md hover:transform hover:scale-102'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className={`text-lg ${currentPage === item.name ? 'filter brightness-110' : ''}`}>
                    {item.icon}
                  </span>
                  <span className="font-medium">{item.name}</span>
                </div>

                {item.badge && (
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    currentPage === item.name
                      ? 'bg-white bg-opacity-20 text-white'
                      : 'bg-blue-100 text-blue-600'
                  }`}>
                    {item.badge}
                  </span>
                )}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Quick Stats */}
      <div className="p-4 mt-auto">
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
          <h3 className="text-sm font-semibold text-gray-800 mb-3">Today's Summary</h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Samples Processed</span>
              <span className="font-semibold text-green-600">156</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Pending Review</span>
              <span className="font-semibold text-orange-600">18</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Active Alerts</span>
              <span className="font-semibold text-red-600">3</span>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}

export default Sidebar;