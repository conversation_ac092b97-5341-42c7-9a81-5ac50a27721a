import React, { createContext, useContext, useReducer } from 'react';
import Patient from './models/PatientData';
import MICData from './models/MICData';
// 通知类型
export const NOTIFICATION_TYPES = {
  DRUG_FILTER_CHANGE: 'DRUG_FILTER_CHANGE',
  AST_RESULT_UPDATE: 'AST_RESULT_UPDATE',
  BREAKING_POINT_MISSING: 'BREAKING_POINT_MISSING'
};

// 定义初始状态
const initialState = {
  patient: null,
  mic: null,
  sample: null,
  notifications: [],
  analysisMessages: '',
  bioArtComments: ''
};

// 定义 reducer 函数
const reducer = (state, action) => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, patient: action.payload };
    case 'SET_MIC':
      return { ...state, mic: action.payload };
    case 'SET_SAMPLE':
      return { ...state, sample: action.payload };
    case 'ADD_NOTIFICATION':
      const notification = {
        id: Date.now() + Math.random(),
        type: action.payload.type,
        message: action.payload.message,
        data: action.payload.data || {},
        timestamp: new Date(),
        read: false
      };
      return { ...state, notifications: [notification, ...state.notifications] };
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification =>
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        )
      };
    case 'MARK_ALL_NOTIFICATIONS_READ':
      return {
        ...state,
        notifications: state.notifications.map(notification => ({ ...notification, read: true }))
      };
    case 'CLEAR_NOTIFICATIONS':
      return { ...state, notifications: [] };
    case 'SET_ANALYSIS_MESSAGES':
      return { ...state, analysisMessages: action.payload };
    case 'SET_BIOART_COMMENTS':
      return { ...state, bioArtComments: action.payload };
    default:
      return state;
  }
};

// 创建 Context
const StateContext = createContext();

// 创建 Provider 组件
export const StateProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <StateContext.Provider value={{ state, dispatch }}>
      {children}
    </StateContext.Provider>
  );
};

// 创建自定义 hook 来使用状态
export const useSharedState = () => {
  const context = useContext(StateContext);
  if (!context) {
    throw new Error('useSharedState must be used within a StateProvider');
  }
  return context;
};

// 便捷的通知相关 hooks
export const useNotifications = () => {
  const { state, dispatch } = useSharedState();

  const addNotification = (type, message, data = {}) => {
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: { type, message, data }
    });
  };

  const markAsRead = (notificationId) => {
    dispatch({
      type: 'MARK_NOTIFICATION_READ',
      payload: notificationId
    });
  };

  const markAllAsRead = () => {
    dispatch({ type: 'MARK_ALL_NOTIFICATIONS_READ' });
  };

  const clearAllNotifications = () => {
    dispatch({ type: 'CLEAR_NOTIFICATIONS' });
  };

  const getUnreadCount = () => {
    return state.notifications.filter(notification => !notification.read).length;
  };

  return {
    notifications: state.notifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    getUnreadCount
  };
};