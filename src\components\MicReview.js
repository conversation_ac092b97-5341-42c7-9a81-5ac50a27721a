// MicReview.js - 微生物检测结果审核页面
import { useState, useEffect } from 'react';
import { useNotifications, useSharedState, NOTIFICATION_TYPES } from '../SharedStateContext';
import './MicReview.css';

function MicReview({ selectedLabId, onNavigateBack }) {
  console.log('🔍 MicReview组件渲染，selectedLabId:', selectedLabId);

  const { addNotification } = useNotifications();
  const { state, dispatch } = useSharedState();

  const [selectedSample, setSelectedSample] = useState(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [bacteriaInfo, setBacteriaInfo] = useState(null);
  const [agents, setAgents] = useState([]);
  const [astResults, setAstResults] = useState([]);
  const [originalAstResults, setOriginalAstResults] = useState([]);
  const [selectedLevels, setSelectedLevels] = useState({
    'I': true,
    'II': true,
    'III': true
  });
  const [interpretingMic, setInterpretingMic] = useState(new Set()); // 正在解释的MIC
  const [newDrugName, setNewDrugName] = useState(''); // 新药物名称输入

  // 从SharedState获取analysisMessages和bioArtComments
  const analysisMessages = state.analysisMessages;
  const bioArtComments = state.bioArtComments;

  // 组件挂载时的调试信息
  useEffect(() => {
    console.log('🔍 MicReview组件挂载，当前SharedState:', {
      hasPatient: !!state.patient,
      hasMic: !!state.mic,
      micCount: state.mic ? state.mic.length : 0,
      selectedLabId
    });
  }, []);

  // 监听bioArtComments状态变化
  useEffect(() => {
    console.log('🔍 bioArtComments状态变化:', bioArtComments);
  }, [bioArtComments]);

  // 监听analysisMessages状态变化
  useEffect(() => {
    console.log('🔍 analysisMessages状态变化:', analysisMessages);
  }, [analysisMessages]);

  // 监听astResults变化并同步到SharedState
  useEffect(() => {
    if (astResults.length > 0 && selectedSample && bacteriaInfo) {
      const micData = astResults.map(astResult => ({
        Agent: astResult.antibiotic,
        MIC: astResult.mic || '',
        SORT: astResult.interpretation || '-',
        BacteriaId: bacteriaInfo?.bacteriaId || bacteriaInfo?._id || '',
        BacteriaName: selectedSample?.organism || '',
        Comments: '',
        Remark: '',
        drugDeduction: '',
        esblAlert: '',
        expertics: [],
        selectedAuthority: 'CLSI'
      }));

      console.log('🔍 astResults变化，同步到SharedState:', micData.length, '条记录');
      dispatch({ type: 'SET_MIC', payload: micData });
    }
  }, [astResults, selectedSample, bacteriaInfo, dispatch]);

  // API配置
  const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  // 根据药物名称推断分类
  const inferDrugCategory = (drugName) => {
    const name = drugName.toLowerCase();

    // 氨基糖苷类
    if (name.includes('霉素') && (name.includes('卡星') || name.includes('米星') || name.includes('大霉素'))) {
      return 'Aminoglycoside';
    }

    // β-内酰胺类
    if (name.includes('西林') || name.includes('培南')) {
      return 'Beta-lactam';
    }

    // 头孢菌素类
    if (name.includes('头孢')) {
      return 'Cephalosporin';
    }

    // 碳青霉烯类
    if (name.includes('培南') && (name.includes('亚胺') || name.includes('美罗') || name.includes('厄他') || name.includes('多立'))) {
      return 'Carbapenem';
    }

    // 氟喹诺酮类
    if (name.includes('沙星') || name.includes('氟沙星')) {
      return 'Fluoroquinolone';
    }

    // 大环内酯类
    if (name.includes('霉素') && (name.includes('红') || name.includes('阿奇') || name.includes('克拉'))) {
      return 'Macrolide';
    }

    // 糖肽类
    if (name.includes('万古') || name.includes('替考')) {
      return 'Glycopeptide';
    }

    // 多粘菌素类
    if (name.includes('粘菌素') || name.includes('多粘菌素')) {
      return 'Polymyxin';
    }

    // 林可霉素类
    if (name.includes('林霉素')) {
      return 'Lincosamide';
    }

    // 四环素类
    if (name.includes('环素')) {
      return 'Tetracycline';
    }

    // 氯霉素类
    if (name.includes('氯霉素')) {
      return 'Chloramphenicol';
    }

    // 磺胺类
    if (name.includes('磺') || name.includes('复方新诺明')) {
      return 'Sulfonamide';
    }

    // 硝基咪唑类
    if (name.includes('硝唑')) {
      return 'Nitroimidazole';
    }

    // 默认分类
    return 'Other';
  };

  // 获取菌株建议
  const fetchBacteriaSuggestions = async (bacteriaName) => {
    try {
      console.log('🔍 获取菌株建议:', bacteriaName);
      const apiUrl = `${API_BASE}/suggestions?query=${encodeURIComponent(bacteriaName)}`;
      console.log('🔍 完整suggestions API URL:', apiUrl);

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🔍 菌株建议API响应:', result);

      if (result && result.length > 0) {
        const firstBacteria = result[0];
        setBacteriaInfo(firstBacteria);
        console.log('✅ 设置菌株信息:', firstBacteria);
        console.log('🔍 菌株对象结构:', JSON.stringify(firstBacteria, null, 2));

        // 获取该菌株的药物agents
        if (firstBacteria.bacteriaId) {
          console.log('🔍 找到菌株ID:', firstBacteria.bacteriaId);
          await fetchAgents(firstBacteria.bacteriaId);
        } else if (firstBacteria._id) {
          console.log('🔍 使用_id作为菌株ID:', firstBacteria._id);
          await fetchAgents(firstBacteria._id);
        } else {
          console.warn('❌ 菌株对象中没有找到bacteriaId或_id');
          console.log('🔍 完整的firstBacteria:', firstBacteria);
        }
      } else {
        console.warn('❌ 没有找到菌株建议');
        console.log('🔍 suggestions API返回结果:', result);
        setBacteriaInfo(null);
        setAgents([]);
        setAstResults([]);
      }
    } catch (error) {
      console.error('❌ 获取菌株建议失败:', error);
      setBacteriaInfo(null);
      setAgents([]);
      setAstResults([]);
    }
  };

  // 获取药物agents
  const fetchAgents = async (bacteriaId) => {
    try {
      console.log('🔍 获取药物agents:', bacteriaId);
      const apiUrl = `${API_BASE}/getAgents?bacteriaId=${bacteriaId}`;
      console.log('🔍 完整API URL:', apiUrl);

      const response = await fetch(apiUrl);
      console.log('🔍 Response status:', response.status);
      console.log('🔍 Response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API响应错误:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('🔍 药物agents API响应:', result);
      console.log('🔍 响应数据类型:', typeof result);
      console.log('🔍 是否为数组:', Array.isArray(result));

      if (result && result.agents && Array.isArray(result.agents)) {
        setAgents(result.agents);
        console.log('✅ 设置药物agents:', result.agents.length, '个');
        console.log('🔍 agents数据:', result.agents);

        // 将agents转换为AST结果格式，并获取详细信息
        const astData = await Promise.all(result.agents.map(async (agentName, index) => {
          // 生成随机的解释结果用于演示
          const interpretations = ['S', 'I', 'R'];
          const randomInterp = interpretations[Math.floor(Math.random() * interpretations.length)];

          // 尝试从Antibiotics API获取详细信息
          let category = inferDrugCategory(agentName);
          let displayName = agentName;

          try {
            // 尝试通过药物名称查找对应的抗生素信息
            const antibioticsResponse = await fetch(`http://localhost:5000/antibiotics`);
            if (antibioticsResponse.ok) {
              const antibiotics = await antibioticsResponse.json();
              const matchedAntibiotic = antibiotics.find(ab =>
                ab.DISPLAY_NAME === agentName ||
                ab.DISPLAY_NAME_EN === agentName ||
                ab.DISPLAY_NAME.includes(agentName) ||
                agentName.includes(ab.DISPLAY_NAME)
              );

              if (matchedAntibiotic) {
                displayName = `${matchedAntibiotic.DISPLAY_NAME} (${matchedAntibiotic.DISPLAY_NAME_EN})`;
                console.log(`✅ 找到匹配的抗生素: ${agentName} -> ${displayName}`);
              }
            }
          } catch (error) {
            console.warn(`⚠️ 获取抗生素详细信息失败: ${agentName}`, error);
          }

          return {
            antibiotic: displayName,
            originalName: agentName, // 保存原始中文名称用于API调用
            mic: '', // 默认为空，让用户输入
            interpretation: randomInterp,
            category: category,
            description: `${agentName} - 抗菌药物`,
            id: `ast_${index}` // 添加唯一ID用于输入框
          };
        }));

        // 检查SharedState中是否已有MIC数据，如果有则恢复
        let finalAstData = astData;
        if (state.mic && Array.isArray(state.mic) && state.mic.length > 0) {
          console.log('🔍 从SharedState恢复MIC数据');
          finalAstData = astData.map(astItem => {
            const sharedMicItem = state.mic.find(micItem => micItem.Agent === astItem.antibiotic);
            if (sharedMicItem) {
              return {
                ...astItem,
                mic: sharedMicItem.MIC || '',
                interpretation: sharedMicItem.SORT || '-'
              };
            }
            return astItem;
          });
        }

        setAstResults(finalAstData);
        setOriginalAstResults(finalAstData); // 保存原始数据
        console.log('✅ 生成AST结果:', finalAstData.length, '个');
        console.log('🔍 AST数据:', finalAstData);
      } else {
        console.warn('❌ 药物agents响应格式不正确');
        console.log('🔍 实际响应结构:', result);
        setAgents([]);
        setAstResults([]);
      }
    } catch (error) {
      console.error('❌ 获取药物agents失败:', error);
      setAgents([]);
      setAstResults([]);
      setOriginalAstResults([]);
    }
  };

  // 查询TableA并过滤药物
  const filterDrugsByTableA = async (microorganismGroup, excludedLevels) => {
    try {
      console.log('🔍 查询TableA过滤药物:', { microorganismGroup, excludedLevels });

      if (excludedLevels.length === 0) {
        // 如果没有排除的级别，显示所有药物
        setAstResults(originalAstResults);
        return;
      }

      const response = await fetch(
        `${API_BASE}/tableA?microorganismGroup=${encodeURIComponent(microorganismGroup)}&resistanceClasses=${excludedLevels.join(',')}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🔍 TableA查询结果:', result);

      if (result.success && result.excludedDrugs) {
        const excludedDrugs = result.excludedDrugs;
        console.log('🔍 需要排除的药物:', excludedDrugs);

        // 过滤AST结果，移除排除的药物
        const filteredAstResults = originalAstResults.filter(astResult => {
          const shouldExclude = excludedDrugs.some(excludedDrug => {
            // 检查药物名称是否匹配
            return astResult.antibiotic.includes(excludedDrug) ||
                   excludedDrug.includes(astResult.antibiotic) ||
                   // 处理类别匹配，如"碳青霉烯类"匹配"美罗培南"等
                   (excludedDrug.includes('类') && astResult.category &&
                    astResult.category.toLowerCase().includes(excludedDrug.replace('类', '').toLowerCase()));
          });

          if (shouldExclude) {
            console.log(`🚫 排除药物: ${astResult.antibiotic} (匹配规则: ${excludedDrugs.join(', ')})`);
          }

          return !shouldExclude;
        }).map(result => ({
          ...result,
          id: result.id || `ast_${Math.random().toString(36).substr(2, 9)}` // 确保有ID
        }));

        console.log(`✅ 过滤后AST结果: ${filteredAstResults.length}/${originalAstResults.length}`);
        setAstResults(filteredAstResults);
      } else {
        console.warn('❌ TableA查询结果格式不正确');
        setAstResults(originalAstResults);
      }
    } catch (error) {
      console.error('❌ TableA查询失败:', error);
      setAstResults(originalAstResults);
    }
  };

  // 处理级别选择变化
  const handleLevelChange = async (level, checked) => {
    const newSelectedLevels = {
      ...selectedLevels,
      [level]: checked
    };

    setSelectedLevels(newSelectedLevels);
    console.log('🔍 级别选择变化:', newSelectedLevels);

    // 获取未选中的级别
    const excludedLevels = Object.keys(newSelectedLevels).filter(key => !newSelectedLevels[key]);
    console.log('🔍 排除的级别:', excludedLevels);

    // 添加通知
    const levelNames = {
      'I': 'Ⅰ级 Wild-type适用',
      'II': 'Ⅱ级 替代或补充方案',
      'III': 'Ⅲ级 多重耐药或专家指定'
    };

    const action = checked ? '启用' : '禁用';
    const message = `${action}了抗生素表型检索: ${levelNames[level]}`;

    addNotification(
      NOTIFICATION_TYPES.DRUG_FILTER_CHANGE,
      message,
      {
        level: level,
        checked: checked,
        excludedLevels: excludedLevels,
        organism: selectedSample?.organism
      }
    );

    // 如果有样本数据，进行过滤
    if (selectedSample && selectedSample.organism) {
      await filterDrugsByTableA(selectedSample.organism, excludedLevels);
    }
  };

  // 添加新药物到AST列表
  const addDrugToAst = () => {
    if (!newDrugName.trim()) {
      alert('请输入药物名称');
      return;
    }

    // 检查是否已存在
    const exists = astResults.some(result =>
      result.antibiotic.toLowerCase().includes(newDrugName.toLowerCase()) ||
      newDrugName.toLowerCase().includes(result.antibiotic.toLowerCase())
    );

    if (exists) {
      alert('该药物已存在于AST列表中');
      return;
    }

    // 创建新的AST记录
    const newAstRecord = {
      antibiotic: newDrugName.trim(),
      originalName: newDrugName.trim(),
      mic: '',
      interpretation: 'S', // 默认敏感
      category: inferDrugCategory(newDrugName.trim()),
      description: `${newDrugName.trim()} - 手动添加`,
      id: `ast_manual_${Date.now()}`
    };

    // 添加到AST列表
    setAstResults(prev => [...prev, newAstRecord]);
    setOriginalAstResults(prev => [...prev, newAstRecord]);

    // 清空输入框
    setNewDrugName('');

    console.log('✅ 手动添加药物到AST列表:', newAstRecord);

    // 添加通知
    addNotification(
      NOTIFICATION_TYPES.AST_RESULT_UPDATE,
      `手动添加药物: ${newDrugName.trim()}`,
      {
        antibiotic: newDrugName.trim(),
        action: 'manual_add'
      }
    );
  };

  // 删除AST记录
  const removeDrugFromAst = (astId) => {
    const drugToRemove = astResults.find(result => result.id === astId);

    if (drugToRemove && window.confirm(`确定要删除 "${drugToRemove.antibiotic}" 吗？`)) {
      setAstResults(prev => prev.filter(result => result.id !== astId));
      setOriginalAstResults(prev => prev.filter(result => result.id !== astId));

      console.log('✅ 删除药物:', drugToRemove.antibiotic);

      // 添加通知
      addNotification(
        NOTIFICATION_TYPES.AST_RESULT_UPDATE,
        `删除药物: ${drugToRemove.antibiotic}`,
        {
          antibiotic: drugToRemove.antibiotic,
          action: 'remove'
        }
      );
    }
  };

  // 处理MIC值变化
  const handleMicChange = async (astId, newMicValue) => {
    console.log('🔍 handleMicChange 被调用:', { astId, newMicValue });
    console.log('🔍 当前状态:', { bacteriaInfo, selectedSample });

    // 立即更新MIC值
    setAstResults(prevResults =>
      prevResults.map(result =>
        result.id === astId
          ? { ...result, mic: newMicValue }
          : result
      )
    );

    // 同时更新原始数据
    setOriginalAstResults(prevResults =>
      prevResults.map(result =>
        result.id === astId
          ? { ...result, mic: newMicValue }
          : result
      )
    );

    // 如果MIC值不为空且有菌株信息，调用breaking-interpret API
    console.log('🔍 检查API调用条件:', {
      micValue: newMicValue.trim(),
      micValueLength: newMicValue.trim().length,
      hasBacteriaInfo: !!bacteriaInfo,
      hasSelectedSample: !!selectedSample,
      bacteriaInfo: bacteriaInfo,
      selectedSample: selectedSample
    });

    if (newMicValue.trim() && bacteriaInfo && selectedSample) {
      console.log('✅ 满足API调用条件，开始调用breaking-interpret');
      console.log('🔍 即将调用API...');
      // 添加到加载状态
      setInterpretingMic(prev => new Set([...prev, astId]));

      try {
        // 找到当前AST记录
        const currentAst = astResults.find(result => result.id === astId) ||
                          originalAstResults.find(result => result.id === astId);

        if (currentAst) {
          // 使用原始中文名称或从显示名称中提取
          const chineseName = currentAst.originalName || currentAst.antibiotic.split(' (')[0].trim();

          const apiParams = {
            selectedAgent: chineseName,
            micValue: newMicValue,
            bacteriaId: bacteriaInfo.bacteriaId || bacteriaInfo._id,
            bacteriaName: selectedSample.organism
          };

          console.log('🔍 API调用参数:', apiParams);
          console.log('🔍 当前AST对象:', currentAst);

          console.log('🔍 调用breaking-interpret API:', apiParams);
          console.log('🔍 完整的bacteriaInfo:', bacteriaInfo);
          console.log('🔍 完整的selectedSample:', selectedSample);
          console.log('🔍 完整的currentAst:', currentAst);

          const apiUrl = `${API_BASE}/breaking-interpret?` + new URLSearchParams(apiParams);
          console.log('🔍 完整API URL:', apiUrl);

          const response = await fetch(apiUrl);

          if (response.ok) {
            const result = await response.json();
            console.log('✅ breaking-interpret API响应成功:', result);
            console.log('🔍 开始处理API响应数据...');

            // 更新Analysis Messages和bioArt Comments
            console.log('🔍 API返回的完整结果:', result);
            console.log('🔍 API返回的remark:', result.remark);
            console.log('🔍 API返回的comments:', result.comments);

            // 处理remark
            if (result.remark && result.remark.trim()) {
              const newMessage = `${currentAst.antibiotic} MIC=${newMicValue}: ${result.remark}`;
              console.log('✅ 更新Analysis Messages:', newMessage);
              const updatedMessages = analysisMessages ? `${analysisMessages}\n${newMessage}` : newMessage;
              dispatch({ type: 'SET_ANALYSIS_MESSAGES', payload: updatedMessages });
            } else {
              console.log('⚠️ remark为空或不存在');
            }

            // 处理comments - 简化逻辑
            if (result.comments && result.comments.trim()) {
              const newComment = `${currentAst.antibiotic}: ${result.comments}`;
              console.log('✅ 准备更新bioArt Comments:', newComment);

              // 直接设置新值，不累积
              dispatch({ type: 'SET_BIOART_COMMENTS', payload: newComment });
              console.log('🔍 setBioArtComments已调用');

            } else {
              console.log('⚠️ comments为空或不存在，实际值:', result.comments);
            }

            if (result.sensitivity !== null && result.sensitivity !== undefined) {
              // 更新解释结果
              setAstResults(prevResults =>
                prevResults.map(astResult =>
                  astResult.id === astId
                    ? { ...astResult, interpretation: result.sensitivity }
                    : astResult
                )
              );

              setOriginalAstResults(prevResults =>
                prevResults.map(astResult =>
                  astResult.id === astId
                    ? { ...astResult, interpretation: result.sensitivity }
                    : astResult
                )
              );

              console.log(`✅ 更新解释结果: ${currentAst.antibiotic} MIC=${newMicValue} → ${result.sensitivity}`);

              // 添加AST结果更新通知
              addNotification(
                NOTIFICATION_TYPES.AST_RESULT_UPDATE,
                `${currentAst.antibiotic} MIC=${newMicValue} 解释结果: ${result.sensitivity}`,
                {
                  antibiotic: currentAst.antibiotic,
                  micValue: newMicValue,
                  sensitivity: result.sensitivity,
                  organism: selectedSample?.organism
                }
              );
            } else {
              // 如果返回null，显示为无法判断
              setAstResults(prevResults =>
                prevResults.map(astResult =>
                  astResult.id === astId
                    ? { ...astResult, interpretation: '-' }
                    : astResult
                )
              );

              setOriginalAstResults(prevResults =>
                prevResults.map(astResult =>
                  astResult.id === astId
                    ? { ...astResult, interpretation: '-' }
                    : astResult
                )
              );

              console.log(`⚠️ 无法判断解释结果: ${currentAst.antibiotic} MIC=${newMicValue} → 无折点数据`);

              // 添加缺少折点数据的通知
              addNotification(
                NOTIFICATION_TYPES.BREAKING_POINT_MISSING,
                `${currentAst.antibiotic} 缺少折点数据，无法自动解释 MIC=${newMicValue}`,
                {
                  antibiotic: currentAst.antibiotic,
                  micValue: newMicValue,
                  organism: selectedSample?.organism
                }
              );
            }
          } else {
            console.warn('❌ breaking-interpret API调用失败:', response.status);
          }
        }
      } catch (error) {
        console.error('❌ breaking-interpret API调用错误:', error);
      } finally {
        // 移除加载状态
        setInterpretingMic(prev => {
          const newSet = new Set(prev);
          newSet.delete(astId);
          return newSet;
        });
      }
    }

    // 立即更新SharedState中的MIC数据，供Quality Control使用
    const updateSharedStateMic = () => {
      // 获取当前最新的astResults状态
      setAstResults(currentAstResults => {
        const micData = currentAstResults.map(astResult => ({
          Agent: astResult.antibiotic,
          MIC: astResult.mic || '',
          SORT: astResult.interpretation || '-',
          BacteriaId: bacteriaInfo?.bacteriaId || bacteriaInfo?._id || '',
          BacteriaName: selectedSample?.organism || '',
          Comments: '', // 可以从API结果中获取
          Remark: '',
          drugDeduction: '',
          esblAlert: '',
          expertics: [],
          selectedAuthority: 'CLSI'
        }));

        console.log('🔍 实时更新SharedState MIC数据:', micData);
        dispatch({ type: 'SET_MIC', payload: micData });

        return currentAstResults; // 返回原始数据，不修改astResults
      });
    };

    // 立即更新
    updateSharedStateMic();
  };

  // 从API获取样品数据
  useEffect(() => {
    const fetchSampleData = async () => {
      if (!selectedLabId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log('🔍 获取样本详情 for Results Review:', selectedLabId);

        // 使用specimenNumber获取患者详细信息
        const response = await fetch(`${API_BASE}/patients/${selectedLabId}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('🔍 Results Review API响应:', result);

        if (result && result.data) {
          // 转换API数据为MicReview需要的格式
          const sampleData = {
            id: result.data._id,
            labId: result.data.specimenNumber || result.data.patient_id,
            organism: result.data.cultureResult || 'Unknown bacteria', // Organism对应bacteria
            bioNumber: result.data.specimenNumber || result.data.patient_id, // Bio Number对应specimen ID
            reviewStatus: 'Reviewed',
            idConfidence: 'Good identification',
            analysisStatus: 'Final',
            patientInfo: {
              patientId: result.data.patient_id,
              patientName: result.data.name,
              age: result.data.age,
              gender: result.data.gender,
              department: result.data.department,
              bed: result.data.bed,
              ward: result.data.ward
            },
            phenotypes: [
              'Ⅰ级 Wild-type适用',
              'Ⅱ级 替代或补充方案',
              'Ⅲ级 多重耐药或专家指定'
            ]
          };

          setSelectedSample(sampleData);
          console.log('✅ 成功设置Results Review样本数据:', sampleData);

          // 设置patient数据到SharedState，供Quality Control使用
          const patientData = {
            patient_id: result.data.patient_id,
            specimenNumber: result.data.specimenNumber,
            name: result.data.name,
            age: result.data.age,
            gender: result.data.gender,
            department: result.data.department,
            sampleType: result.data.sampleType,
            cultureResult: result.data.cultureResult,
            bed: result.data.bed,
            ward: result.data.ward,
            medicalRecordNumber: result.data.medicalRecordNumber,
            smearResult: result.data.smearResult,
            remark: result.data.remark,
            hospital_code: result.data.hospital_code,
            consumableboardNumber: result.data.consumableboardNumber,
            bacteriaCount: result.data.bacteriaCount,
            bacteriaCode: result.data.bacteriaCode,
            sampleCode: result.data.sampleCode
          };

          console.log('🔍 设置patient数据到SharedState:', patientData);
          dispatch({ type: 'SET_USER', payload: patientData });

          // 获取菌株建议和药物agents
          const bacteriaName = result.data.cultureResult;
          if (bacteriaName && bacteriaName !== 'Unknown bacteria') {
            await fetchBacteriaSuggestions(bacteriaName);
          }
        } else {
          console.warn('❌ API响应中没有data字段:', result);
          setSelectedSample(null);
        }
      } catch (error) {
        console.error('❌ 获取样本详情失败:', error);
        setSelectedSample(null);
      } finally {
        setLoading(false);
      }
    };

    fetchSampleData();
  }, [selectedLabId, API_BASE]);

  // 处理文件导入
  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log('导入文件:', file.name);
      // 这里可以添加文件处理逻辑
      setShowImportModal(false);
    }
  };

  // 处理导入按钮点击
  const handleImportClick = () => {
    setShowImportModal(true);
  };

  if (loading) return <div className="flex justify-center items-center h-64"><div className="text-lg">Loading...</div></div>;
  if (!selectedSample) return <div className="flex justify-center items-center h-64"><div className="text-lg">No sample data available</div></div>;

  return (
    <div className="mic-review-container">
      {/* 页面标题 */}
      <div className="mb-4 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Results Review
            {selectedLabId && (
              <span className="text-lg font-normal text-blue-600 ml-2">
                - {selectedLabId}
              </span>
            )}
          </h1>
          <p className="text-gray-600">Microbiology Test Results Analysis</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleImportClick}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center space-x-2 transition-colors"
          >
            <span>📁</span>
            <span>Import Results</span>
          </button>
        </div>
      </div>

      <div className="mic-review-grid">
        {/* 左侧 - 样品信息面板 */}
        <div className="sample-info-panel">
          {/* 基本信息 */}
          <div className="panel-section">
            <h3 className="section-header">Sample Information</h3>
            
            <div>
              <div className="info-row">
                <span className="info-label">Specimen ID:</span>
                <span className="info-value">{selectedSample.labId}</span>
              </div>
              
              <div className="info-row">
                <span className="info-label">Organism:</span>
                <span className="info-value">{selectedSample.organism}</span>
              </div>
              
              <div className="info-row">
                <span className="info-label">Review Status:</span>
                <span className="status-badge status-reviewed">
                  {selectedSample.reviewStatus}
                </span>
              </div>
              
              <div className="info-row">
                <span className="info-label">Bio Number:</span>
                <span className="info-value">{selectedSample.bioNumber}</span>
              </div>

              {/* 患者信息 */}
              {selectedSample.patientInfo && (
                <>
                  <div className="info-row">
                    <span className="info-label">Patient:</span>
                    <span className="info-value">{selectedSample.patientInfo.patientName} ({selectedSample.patientInfo.patientId})</span>
                  </div>

                  <div className="info-row">
                    <span className="info-label">Age/Gender:</span>
                    <span className="info-value">{selectedSample.patientInfo.age} / {selectedSample.patientInfo.gender}</span>
                  </div>

                  <div className="info-row">
                    <span className="info-label">Department:</span>
                    <span className="info-value">{selectedSample.patientInfo.department}</span>
                  </div>

                  <div className="info-row">
                    <span className="info-label">Bed/Ward:</span>
                    <span className="info-value">{selectedSample.patientInfo.bed} / {selectedSample.patientInfo.ward}</span>
                  </div>
                </>
              )}

              <div className="info-row">
                <span className="info-label">ID Confidence:</span>
                <span className="info-value">{selectedSample.idConfidence}</span>
              </div>
              
              <div className="info-row">
                <span className="info-label">Analysis Status:</span>
                <span className="info-value">{selectedSample.analysisStatus}</span>
              </div>
            </div>
          </div>

          {/* AES Findings */}
          <div className="panel-section">
            <div className="flex items-center justify-between mb-3">
              <h3 className="section-header">AES Findings:</h3>
              <span className="status-badge" style={{background: '#dbeafe', color: '#1e40af'}}>
                Consistent with correction
              </span>
            </div>
            
            <div>
              <h4 className="info-label mb-2">抗生素表型检索:</h4>
              <div>
                <div className="phenotype-checkbox">
                  <input
                    type="checkbox"
                    checked={selectedLevels.I}
                    onChange={(e) => handleLevelChange('I', e.target.checked)}
                  />
                  <span>Ⅰ级 Wild-type适用</span>
                </div>
                <div className="phenotype-checkbox">
                  <input
                    type="checkbox"
                    checked={selectedLevels.II}
                    onChange={(e) => handleLevelChange('II', e.target.checked)}
                  />
                  <span>Ⅱ级 替代或补充方案</span>
                </div>
                <div className="phenotype-checkbox">
                  <input
                    type="checkbox"
                    checked={selectedLevels.III}
                    onChange={(e) => handleLevelChange('III', e.target.checked)}
                  />
                  <span>Ⅲ级 多重耐药或专家指定</span>
                </div>
              </div>
            </div>
          </div>

          {/* Analysis Messages */}
          <div className="panel-section">
            <h3 className="section-header">Analysis Messages:</h3>
            <div className="button-group">
              <button
                className="btn-small btn-clear"
                onClick={() => dispatch({ type: 'SET_ANALYSIS_MESSAGES', payload: '' })}
              >
                Clear
              </button>
            </div>
            <div className="comment-box">
              {analysisMessages || '暂无分析消息'}
            </div>
          </div>

          {/* bioArt Comment */}
          <div className="panel-section">
            <h3 className="section-header">bioArt Comment:</h3>
            <div className="button-group">
              <button className="btn-small btn-edit">Edit</button>
              <button
                className="btn-small btn-clear"
                onClick={() => dispatch({ type: 'SET_BIOART_COMMENTS', payload: '' })}
              >
                Clear
              </button>
            </div>
            <div className="comment-box">
              {bioArtComments || '暂无bioArt评论'}
            </div>
          </div>
        </div>

        {/* 右侧 - AST结果表格 */}
        <div className="ast-results-panel">
          <div className="panel-section">
            <h3 className="section-header">AST Offline Tests:</h3>
          </div>
          
          <div className="ast-table-container">
            <table className="ast-table">
              <thead>
                <tr>
                  <th>Antibiotic</th>
                  <th style={{width: '120px'}}>MIC</th>
                  <th>Interp.</th>
                  <th>Category</th>
                  <th style={{width: '60px', textAlign: 'center'}}>操作</th>
                </tr>
              </thead>
              <tbody>
                {astResults.length > 0 ? (
                  astResults.map((result, index) => (
                    <tr key={index}>
                      <td>
                        {result.antibiotic}
                        {result.description && (
                          <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                            {result.description}
                          </div>
                        )}
                      </td>
                      <td style={{textAlign: 'center', padding: '4px 2px'}}>
                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2px', alignItems: 'center' }}>
                          {/* 左列：输入框 */}
                          <input
                            type="text"
                            value={result.mic}
                            onChange={(e) => {
                              console.log('🔍 Input onChange triggered:', e.target.value);
                              console.log('🔍 当前result:', result);
                              handleMicChange(result.id, e.target.value);
                            }}
                            placeholder="MIC值"
                            style={{
                              width: '100%',
                              padding: '2px 4px',
                              border: '1px solid #ddd',
                              borderRadius: '3px',
                              textAlign: 'center',
                              fontSize: '11px'
                            }}
                          />
                          {/* 右列：快捷选择 */}
                          <select
                            value=""
                            onChange={(e) => {
                              if (e.target.value) {
                                handleMicChange(result.id, e.target.value);
                                e.target.value = ''; // 重置选择框
                              }
                            }}
                            style={{
                              width: '100%',
                              padding: '2px',
                              border: '1px solid #ddd',
                              borderRadius: '3px',
                              fontSize: '9px',
                              backgroundColor: '#f8f9fa'
                            }}
                          >
                            <option value="">快选</option>
                            <option value="≤0.25">≤0.25</option>
                            <option value="≤0.5">≤0.5</option>
                            <option value="≤1">≤1</option>
                            <option value="≤2">≤2</option>
                            <option value="≤4">≤4</option>
                            <option value="≤8">≤8</option>
                            <option value="≤16">≤16</option>
                            <option value="≤20">≤20</option>
                            <option value=">32">&gt;32</option>
                            <option value=">64">&gt;64</option>
                            <option value="POS">POS</option>
                            <option value="NEG">NEG</option>
                          </select>
                        </div>
                      </td>
                      <td style={{textAlign: 'center'}}>
                        {interpretingMic.has(result.id) ? (
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '5px' }}>
                            <div style={{
                              width: '12px',
                              height: '12px',
                              border: '2px solid #f3f3f3',
                              borderTop: '2px solid #3498db',
                              borderRadius: '50%',
                              animation: 'spin 1s linear infinite'
                            }}></div>
                            <span style={{ fontSize: '11px', color: '#666' }}>解释中...</span>
                          </div>
                        ) : (
                          <span className={`interpretation-badge interpretation-${result.interpretation.toLowerCase()}`}>
                            {result.interpretation}
                          </span>
                        )}
                      </td>
                      <td>{result.category}</td>
                      <td style={{textAlign: 'center'}}>
                        <button
                          onClick={() => removeDrugFromAst(result.id)}
                          style={{
                            padding: '2px 6px',
                            fontSize: '10px',
                            backgroundColor: '#dc3545',
                            color: 'white',
                            border: 'none',
                            borderRadius: '3px',
                            cursor: 'pointer'
                          }}
                          title="删除此药物"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="5" style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
                      {loading ? '正在加载AST数据...' : '暂无AST测试数据'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* 菌株信息和药物agents */}
          {bacteriaInfo && (
            <div className="bacteria-info-section" style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
              <h4 style={{ marginBottom: '15px', color: '#333', fontSize: '16px', fontWeight: 'bold' }}>
                菌株信息和药物建议
              </h4>

              {/* 菌株基本信息 - 自动填充 */}
              <div style={{ marginBottom: '15px' }}>
                <h5 style={{ marginBottom: '8px', color: '#555', fontSize: '14px', fontWeight: 'bold' }}>
                  菌株信息: (自动填充)
                </h5>
                <div style={{ fontSize: '13px', color: '#666' }}>
                  <p><strong>菌株名称:</strong> {bacteriaInfo.name || selectedSample?.organism || 'N/A'}</p>
                  <p><strong>菌株ID:</strong> {bacteriaInfo.bacteriaId || bacteriaInfo._id || 'N/A'}</p>
                  {bacteriaInfo.description && (
                    <p><strong>描述:</strong> {bacteriaInfo.description}</p>
                  )}
                </div>
              </div>

              {/* 推荐药物 - 动态输入框 */}
              <div>
                <h5 style={{ marginBottom: '8px', color: '#555', fontSize: '14px', fontWeight: 'bold' }}>
                  推荐药物:
                </h5>

                {/* 药物输入框和添加按钮 */}
                <div style={{
                  display: 'flex',
                  gap: '10px',
                  marginBottom: '15px',
                  padding: '10px',
                  backgroundColor: '#fff',
                  border: '1px solid #ddd',
                  borderRadius: '4px'
                }}>
                  <input
                    type="text"
                    value={newDrugName}
                    onChange={(e) => setNewDrugName(e.target.value)}
                    placeholder="输入药物名称 (如: 阿莫西林, 头孢曲松等)"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addDrugToAst();
                      }
                    }}
                    style={{
                      flex: 1,
                      padding: '6px 10px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      fontSize: '12px'
                    }}
                  />
                  <button
                    onClick={addDrugToAst}
                    style={{
                      padding: '6px 15px',
                      backgroundColor: '#28a745',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    添加到AST
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 如果没有菌株信息 */}
          {!bacteriaInfo && selectedSample && (
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#fff3cd', borderRadius: '8px', border: '1px solid #ffeaa7' }}>
              <div style={{ fontSize: '13px', color: '#856404' }}>
                <strong>提示:</strong> 未找到该菌株的详细信息和药物建议。菌株名称: {selectedSample.organism}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 导入模态框 */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-90vw">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Import Results</h3>
              <button 
                onClick={() => setShowImportModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-3">
                Select a file to import microbiology test results. Supported formats: CSV, Excel, XML.
              </p>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  id="fileInput"
                  accept=".csv,.xlsx,.xls,.xml"
                  onChange={handleFileImport}
                  className="hidden"
                />
                <label 
                  htmlFor="fileInput" 
                  className="cursor-pointer flex flex-col items-center space-y-2"
                >
                  <span className="text-3xl">📁</span>
                  <span className="text-sm font-medium text-gray-700">
                    Click to select file or drag and drop
                  </span>
                  <span className="text-xs text-gray-500">
                    CSV, Excel, XML files up to 10MB
                  </span>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button 
                onClick={() => setShowImportModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium"
              >
                Cancel
              </button>
              <button 
                onClick={() => document.getElementById('fileInput').click()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium"
              >
                Browse Files
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MicReview;
