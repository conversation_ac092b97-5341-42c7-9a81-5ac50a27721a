/* QualityControlNew.css - 质量控制页面样式 */

.quality-control-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: #666;
  text-align: center;
}

/* 错误提示 */
.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: none;
  border: none;
  color: #c33;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
}

/* 头部状态区域 */
.qc-header-section {
  display: flex;
  gap: 32px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.qc-left-column {
  flex: 1;
}

.qc-right-column {
  flex: 1;
  max-width: 300px;
}

.qc-field-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.qc-label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
  font-size: 14px;
}

.qc-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.qc-badge-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.qc-badge-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.qc-checkbox-warning {
  accent-color: red;
  cursor: pointer;
  margin-right: 8px;
}

.qc-inconsistent {
  color: red;
  font-weight: 500;
  font-size: 14px;
}

/* 表型区域 */
.qc-phenotype-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.qc-phenotype-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.qc-phenotype-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qc-checkbox {
  margin: 0;
}

.qc-phenotype-text {
  font-size: 10px;
  color: #555;
}

/* 分析提示区域 */
.qc-analysis-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.qc-analysis-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.qc-analysis-row {
  display: flex;
  align-items: center;
  min-height: 24px;
}

.qc-analysis-label {
  width: 80px;
  color: #888;
  font-size: 13px;
}

.qc-analysis-value {
  color: #333;
  font-size: 13px;
}

/* BioFIT备注区域 */
.qc-comment-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.qc-comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.qc-comment-buttons {
  display: flex;
  gap: 8px;
}

.qc-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.qc-btn-primary {
  background-color: #007bff;
  color: white;
}

.qc-btn-primary:hover {
  background-color: #0056b3;
}

.qc-btn-secondary {
  background-color: #6c757d;
  color: white;
}

.qc-btn-secondary:hover {
  background-color: #545b62;
}

.qc-comment-content {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.qc-notes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.qc-note-item {
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  padding: 4px 0;
}

/* AST表格区域 */
.qc-table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.qc-table-container {
  overflow-x: auto;
}

.qc-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.qc-table th {
  background-color: #f8f9fa;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  color: #495057;
}

.qc-td {
  padding: 8px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.qc-mic-cell {
  cursor: pointer;
  transition: background-color 0.2s;
}

.qc-mic-cell:hover {
  background-color: #f8f9fa;
}

.qc-mic-out-range {
  background-color: #d4edda !important;
  color: #155724;
  font-weight: 600;
}

.qc-mic-input {
  width: 60px;
  padding: 2px 4px;
  border: 1px solid #007bff;
  border-radius: 3px;
  font-size: 13px;
  text-align: center;
}

.qc-sensitivity {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
}

.qc-sensitivity-s {
  background-color: #d4edda;
  color: #155724;
}

.qc-sensitivity-r {
  background-color: #f8d7da;
  color: #721c24;
}

.qc-sensitivity-i {
  background-color: #fff3cd;
  color: #856404;
}

.qc-empty-table {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .qc-header-section {
    flex-direction: column;
    gap: 20px;
  }
  
  .qc-right-column {
    max-width: none;
  }
  
  .qc-comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
