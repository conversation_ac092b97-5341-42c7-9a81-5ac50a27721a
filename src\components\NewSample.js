// NewSample.js - 新增样品表单组件
import { useState } from 'react';


function NewSample({ onClose, onSampleAdded }) {
  const [formData, setFormData] = useState({
    labId: '',
    bacteriaName: '',
    bacteriaCode: '',
    sampleSource: 'Urine',
    patientId: '',
    patientName: '',
    age: '',
    gender: '男',
    department: '',
    bed: '',
    ward: '',
    medicalRecordNumber: '',
    smearResult: '',
    remark: '',
    collectionDate: new Date().toISOString().split('T')[0],
    status: 'Pending',
    priority: 'Normal',
    testDataList: []
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // API配置 - 从环境变量获取
  const API_BASE = process.env.REACT_APP_API_URL;

  // 样本来源选项
  const sampleSources = ['Urine', 'Blood', 'CSF', 'Sputum', 'Wound', 'Stool', 'Throat'];
  
  // 状态选项
  const statusOptions = ['Pending', 'Processing', 'Completed', 'Failed'];
  
  // 优先级选项
  const priorityOptions = ['Normal', 'High', 'Urgent'];

  // 常见菌种
  const commonBacteria = [
    'E. coli',
    'S. aureus',
    'Streptococcus',
    'K. pneumoniae',
    'P. aeruginosa',
    'Enterococcus',
    'C. albicans',
    'Other'
  ];

  // 生成Specimen ID
  const generateLabId = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `LAB${year}${month}${day}${random}`;
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // 添加新的检测数据项
  const addTestData = () => {
    const newTestData = {
      testMethod: '',
      testResult: '',
      testSupplement: '',
      testSupplementResult: ''
    };
    setFormData(prev => ({
      ...prev,
      testDataList: [...prev.testDataList, newTestData]
    }));
  };

  // 删除检测数据项
  const removeTestData = (index) => {
    setFormData(prev => ({
      ...prev,
      testDataList: prev.testDataList.filter((_, i) => i !== index)
    }));
  };

  // 更新检测数据项
  const updateTestData = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      testDataList: prev.testDataList.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  // 验证表单
  const validateForm = () => {
    const newErrors = {};

    if (!formData.labId.trim()) {
      newErrors.labId = 'Specimen ID is required';
    }

    if (!formData.bacteriaName.trim()) {
      newErrors.bacteriaName = 'Bacteria name is required';
    }

    if (!formData.patientId.trim()) {
      newErrors.patientId = 'Patient ID is required';
    }

    if (!formData.patientName.trim()) {
      newErrors.patientName = 'Patient Name is required';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'Department is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // 转换前端表单数据为后端Patient模型格式
      const patientData = {
        patient_id: formData.patientId,
        specimenNumber: formData.labId,
        name: formData.patientName || 'Unknown',
        age: formData.age || '0',
        gender: formData.gender || '男',
        department: formData.department,
        sampleType: formData.sampleSource,
        cultureResult: formData.bacteriaName,
        bacteriaId: formData.bacteriaId, // 新增bacteriaId字段
        bed: formData.bed || '',
        ward: formData.ward || '',
        medicalRecordNumber: formData.medicalRecordNumber || '',
        smearResult: formData.smearResult || '',
        remark: formData.remark || '',
        hospital_code: '123456', // 默认医院代码
        consumableboardNumber: Math.floor(Math.random() * 9999).toString(),
        sampleCode: formData.sampleSource === '血液' ? 'bl' :
                    formData.sampleSource === '尿液' ? 'ur' :
                    formData.sampleSource === '脑脊液' ? 'csf' :
                    formData.sampleSource === 'Blood' ? 'bl' :
                    formData.sampleSource === 'Urine' ? 'ur' :
                    formData.sampleSource === 'CSF' ? 'csf' : 'ot',
        testDataList: formData.testDataList
      };

      console.log('🔧 发送患者数据:', patientData);

      const response = await fetch(`http://localhost:5000/patients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(patientData)
      });

      const result = await response.json();
      console.log('🔧 API响应:', result);

      if (response.ok && result.message === '保存成功') {
        alert('样本创建成功！');
        onSampleAdded(); // 通知父组件刷新列表
        onClose(); // 关闭表单
      } else {
        alert(`创建失败: ${result.error || result.message}`);
      }
    } catch (error) {
      console.error('❌ 创建样本失败:', error);
      alert(`创建样本失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    setFormData({
      labId: '',
      bacteriaId: '', // 新增bacteriaId字段
      bacteriaName: '',
      sampleSource: 'Urine',
      patientId: '',
      patientName: '',
      age: '',
      gender: '男',
      department: '',
      bed: '',
      ward: '',
      medicalRecordNumber: '',
      smearResult: '',
      remark: '',
      collectionDate: new Date().toISOString().split('T')[0],
      status: 'Pending',
      priority: 'Normal',
      testDataList: []
    });
    setErrors({});
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white px-6 py-4 rounded-t-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">新建标本信息</h2>
            <button 
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Specimen ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                标本编号 *
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  name="labId"
                  value={formData.labId}
                  onChange={handleInputChange}
                  className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.labId ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="请输入标本编号"
                />
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, labId: generateLabId() }))}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm"
                >
                  自动生成
                </button>
              </div>
              {errors.labId && <p className="text-red-500 text-sm mt-1">{errors.labId}</p>}
            </div>

            {/* Bacteria Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                菌种名称 *
              </label>
              <select
                name="bacteriaName"
                value={formData.bacteriaName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.bacteriaName ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">请选择菌种</option>
                {commonBacteria.map(bacteria => (
                  <option key={bacteria} value={bacteria}>{bacteria}</option>
                ))}
              </select>
              {errors.bacteriaName && <p className="text-red-500 text-sm mt-1">{errors.bacteriaName}</p>}
            </div>

            {/* Bacteria ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                菌株ID
              </label>
              <input
                type="text"
                name="bacteriaId"
                value={formData.bacteriaCode}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入菌株ID (可选)"
              />
            </div>

            {/* Sample Source */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                样本类型 *
              </label>
              <select
                name="sampleSource"
                value={formData.sampleSource}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {sampleSources.map(source => (
                  <option key={source} value={source}>{source}</option>
                ))}
              </select>
            </div>

            {/* Patient ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                患者ID *
              </label>
              <input
                type="text"
                name="patientId"
                value={formData.patientId}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.patientId ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入患者ID (如：P123456)"
              />
              {errors.patientId && <p className="text-red-500 text-sm mt-1">{errors.patientId}</p>}
            </div>

            {/* Patient Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                患者姓名 *
              </label>
              <input
                type="text"
                name="patientName"
                value={formData.patientName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.patientName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入患者姓名 (如：张三)"
              />
              {errors.patientName && <p className="text-red-500 text-sm mt-1">{errors.patientName}</p>}
            </div>

            {/* Age */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                年龄
              </label>
              <input
                type="number"
                name="age"
                value={formData.age}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入年龄 (如：45)"
                min="0"
                max="150"
              />
            </div>

            {/* Gender */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                性别
              </label>
              <select
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="男">男</option>
                <option value="女">女</option>
              </select>
            </div>

            {/* Bed */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                床位
              </label>
              <input
                type="text"
                name="bed"
                value={formData.bed}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入床位号 (如：101)"
              />
            </div>

            {/* Ward */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                病房
              </label>
              <input
                type="text"
                name="ward"
                value={formData.ward}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入病房 (如：内科病房)"
              />
            </div>

            {/* Medical Record Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                病历号
              </label>
              <input
                type="text"
                name="medicalRecordNumber"
                value={formData.medicalRecordNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入病历号 (如：MRN001)"
              />
            </div>

            {/* Smear Result */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                涂片结果
              </label>
              <input
                type="text"
                name="smearResult"
                value={formData.smearResult}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入涂片结果 (如：革兰阴性杆菌)"
              />
            </div>

            {/* Remark */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                备注
              </label>
              <textarea
                name="remark"
                value={formData.remark}
                onChange={handleInputChange}
                rows="3"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入备注或说明"
              />
            </div>

            {/* Collection Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                采集日期 *
              </label>
              <input
                type="date"
                name="collectionDate"
                value={formData.collectionDate}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {statusOptions.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>

            {/* Priority */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                优先级
              </label>
              <select
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {priorityOptions.map(priority => (
                  <option key={priority} value={priority}>{priority}</option>
                ))}
              </select>
            </div>

            {/* Department */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                科室 *
              </label>
              <input
                type="text"
                name="department"
                value={formData.department}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.department ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入科室 (如：急诊、ICU)"
              />
              {errors.department && <p className="text-red-500 text-sm mt-1">{errors.department}</p>}
            </div>
          </div>

          {/* Test Data List Section */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">检测数据列表</h3>
              <button
                type="button"
                onClick={addTestData}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
              >
                <span className="mr-2">+</span>
                添加检测数据
              </button>
            </div>

            {formData.testDataList.length === 0 ? (
              <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                <p>暂无检测数据</p>
                <p className="text-sm mt-1">点击"添加检测数据"按钮开始添加</p>
              </div>
            ) : (
              <div className="space-y-4">
                {formData.testDataList.map((testData, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-700">检测数据 #{index + 1}</h4>
                      <button
                        type="button"
                        onClick={() => removeTestData(index)}
                        className="text-red-600 hover:text-red-800 font-medium"
                      >
                        删除
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Test Method */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          检测方法
                        </label>
                        <input
                          type="text"
                          value={testData.testMethod}
                          onChange={(e) => updateTestData(index, 'testMethod', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="输入检测方法 (e.g., EDTA-碳青霉烯灭活试验)"
                        />
                      </div>

                      {/* Test Result */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          检测结果
                        </label>
                        <input
                          type="text"
                          value={testData.testResult}
                          onChange={(e) => updateTestData(index, 'testResult', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="输入检测结果 (e.g., 阳性/阴性)"
                        />
                      </div>

                      {/* Test Supplement */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          补充信息
                        </label>
                        <input
                          type="text"
                          value={testData.testSupplement}
                          onChange={(e) => updateTestData(index, 'testSupplement', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="输入补充信息"
                        />
                      </div>

                      {/* Test Supplement Result */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          补充结果
                        </label>
                        <input
                          type="text"
                          value={testData.testSupplementResult}
                          onChange={(e) => updateTestData(index, 'testSupplementResult', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="输入补充结果"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleReset}
              className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              重置
            </button>
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              {loading ? '保存中...' : '保存样本'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default NewSample;
