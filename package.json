{"name": "electron-react-app", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.26.0", "chart.js": "^4.5.0", "cors": "^2.8.5", "express": "^5.1.0", "lucide-react": "^0.515.0", "mongoose": "^8.15.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "npm run build && electron ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "electron": "^36.4.0", "electron-reload": "^2.0.0-alpha.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "main": "main.js", "homepage": "./"}