// app.js or server.js
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const Breaking = require('./models/Breaking'); // 导入 Breaking 模型
const BacterialTaxonomy = require('./models/BacterialTaxonomy');
const Antibiotics = require('./models/Antibiotics');
const Patient = require('./models/PatientSchema');  
const PatientMic = require('./models/PatitentMic');
const PatientSample = require('./models/PatientSample'); 
const DataModel = require('./models/UserMic');
const TableA = require('./models/TableA');
const TableB = require('./models/TableB');
const Organism = require('./models/organismSchema');
const Antimicrobial = require('./models/antimicrobial'); 
const WhonetRules = require('./models/WhonetRules')
const Carbapenem = ['厄他培南', '多立培南*', '亚胺培南', '美罗培南', '比阿培南', '帕尼培南'];
const path = require('path');
const IntrinsicResistance = require('./models/IntrinsicResistance');
const organismSchema = require('./models/organismSchema');
const experticsSchema = require('./models/Expertics');
const TDictSpec = require('./models/TDictSpec');
const Bacteria = require('./models/BacteriaSchema');
const SpecimenData = require('./models/specimenDataSchema');
const fs = require('fs');
const BacteriaSchema = require('./models/BacteriaSchema');
const MicDistribution = require('./models/micDistributionSchema'); 
const app = express();
let recent_agents = [];
// 连接到 MongoDB 数据库
mongoose.connect('mongodb://127.0.0.1:27017/RD_DB', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

app.use(cors()); // 允许所有来源进行请求
app.use(express.json());

// Serve static files from the React app
app.use(express.static(path.join(__dirname, 'build')));


// 辅助函数：将字符格式的范围解析为数字
function parseRange(range) {
  if (!range) return null; // 确保 range 存在

  range = range.trim(); // 去除可能的空白字符

  if (range.startsWith('≤')) {
    return { type: 'max', value: parseFloat(range.slice(1)) };
  } else if (range.startsWith('≥')) {
    return { type: 'min', value: parseFloat(range.slice(1)) };
  } else if (!isNaN(parseFloat(range))) {
    return { type: 'exact', value: parseFloat(range) };
  }
  return null;
}

const checkESBLAlert = (micValues) => {
  // Primary Screening Conditions (Trigger Alert)
  const primaryScreening = [
    { agent: "头孢曲松", mic: micValues["头孢曲松"] },
    { agent: "头孢噻肟", mic: micValues["头孢噻肟"] },
    { agent: "头孢他啶", mic: micValues["头孢他啶"] }
  ];

  const screeningTriggered = primaryScreening.some(item => item.mic >= 2);

  // Phenotypic Consistency Validation
  const aztreonamResistant = micValues["氨曲南"] === "R"; // Resistant
  const cefoxitinSensitive = micValues["头孢西丁"] === "S"; // Sensitive
  const phenotypicValidation = aztreonamResistant && cefoxitinSensitive;

  // Determine the ESBL Alert Message
  let esblAlert = null;
  if (screeningTriggered) {
    if (!phenotypicValidation) {
      esblAlert = "请使用头孢他啶/克拉维酸协同试验进行确认(CLSI M100-Ed33,第12B节)";
    } else {
      esblAlert = "ESBL阳性:符合超广谱 β-内酰胺酶(ESBL)产生的特征";
    }
  }

  return esblAlert;
};

const getExpertAlerts = async( ORG_CODES, ABX_RESULT)=>{
  const antibiotic = await WhonetRules.find({ ORG_CODES, ABX_RESULT });

  // 如果未找到匹配的记录，返回 404 错误
  if (!antibiotic || antibiotic.length === 0) {
    return null;
  }
  return antibiotic;
 
};

// const getABXResults=async() =>{
//   const data = await WhonetRules.aggregate([{$project:{ABX_RESULT:1,_id:0}}]) ;
//   const abxResults = data.map(result => result.ABX_RESULT);
//   fs.writeFileSync('ABX_RESULT_output.txt', abxResults.join('\n'), 'utf8');

//   console.log('Data successfully saved to ABX_RESULT_output.txt');
// }

const getExpertics = async (chnBacteria, chnAntics) => {
  try {
   
    let dic = await BacterialTaxonomy.findOne({ 'name': { $regex: chnBacteria, $options: 'i' } });
   
    if (!dic) return null;
    let dic_parent = await getLevelId(dic.parent_id)
    if (!dic_parent) return null;

    // Find all corresponding antibiotic entries based on the provided Chinese name
    let ants = await Antibiotics.find({ 'DISPLAY_NAME': { $regex: chnAntics, $options: 'i' } });
    if (ants.length === 0) return null;

    // Store the expertics results
    const expertics = [];
  
    // Use Promise.all to run all asynchronous operations concurrently
    const experticPromises = ants.map(async (ant) => {
      // Find the expertics information in the WhonetRules collection for each antibiotic
      const expertic = await WhonetRules.findOne({
        'ORGANISMS':  { $regex: dic_parent.name, $options: 'i' },
        'ABX_RESULT': { $regex: ant.ANTI_CODE, $options: 'i' },
      });
   
      if (expertic) {
        expertics.push(expertic);
      }
    });

    // Wait for all asynchronous operations to finish
    await Promise.all(experticPromises);

    return expertics;

  } catch (error) {
    // Handle errors if any database query fails
    console.error("Error occurred while retrieving expertics:", error);
    return null;
  }
};





const getCLSIComments = async (bacteriaId, agent) => {
  try {
   
    const chineseAgent = agent
      .split('\n')
      .find(line => /[\u4e00-\u9fa5]/.test(line))
      ?.trim(); // Added optional chaining to handle undefined

    if (!chineseAgent) {
      return ''; // Return an empty string if no Chinese agent is found
    }

    // Query Antimicrobial collection with both agent and BacterId conditions
    const results = await Antimicrobial.find(
      {
        'Antimicrobial Agent': new RegExp(chineseAgent, 'i'),
        BacterId: bacteriaId
      },
      { Comments: 1, _id: 0 }
    );

    // Query PatientSample collection with both agent and BacterId conditions
    const bk_results = await PatientSample.find(
      {
        originalAgent: new RegExp(chineseAgent, 'i'),
        BacterId: bacteriaId
      },
      { Comments: 1, _id: 0 }
    );

    // Combine comments from both collections
    const allComments = [
      ...results.map(doc => doc.Comments).filter(Boolean),
      ...bk_results.map(doc => doc.Comments).filter(Boolean)
    ];

    // Deduplicate and concatenate comments
    const uniqueComments = [...new Set(allComments)];
    return uniqueComments.join('; '); // Concatenate with semicolon for clarity
  } catch (error) {
    console.error("Error fetching comments:", error);
    throw new Error("Unable to fetch comments.");
  }
};



const getLevelId= async(parent_id)=>{
  const currentBacteria = await BacterialTaxonomy.findOne({ _id: new mongoose.Types.ObjectId(parent_id) });

      if (!currentBacteria) {
        return res.status(404).json({ error: 'Bacteria not found' });
      }

      // 找到 level == 2 的祖先
      let ancestor = currentBacteria;
      while (ancestor.level > 2) {
        ancestor = await BacterialTaxonomy.findOne({ _id: ancestor.parent_id });
        if (!ancestor) {
          return res.status(404).json({ error: 'Ancestor not found' });
        }
      }
      return ancestor;
};

const processMicData = async (data,sense) => {
  const { bacteriaId, bacteriaName, selectedAgent, micValue, diskContent, selectedMethod } = data;
  
  if (!bacteriaId || !selectedAgent || !selectedMethod) {
    throw new Error('Invalid input. Ensure all fields are provided.');
  }

  const expertics =await getExpertics(bacteriaName,selectedAgent);

  // Manage recent agents
  if (selectedAgent && !recent_agents.includes(selectedAgent)) {
    recent_agents.unshift(selectedAgent);
    if (recent_agents.length > 10) {
      recent_agents.pop();
    }
  }

  try {
    const parent_bacteria = await getLevelId(bacteriaId);
    const chineseAgent = selectedAgent.split('\n').find((line) => /[\u4e00-\u9fa5]/.test(line)).trim();

    // Check intrinsic resistance
    let result = await IntrinsicResistance.findOne({
      bacteriaId: bacteriaId,
      resistance: { $regex: chineseAgent, $options: 'i' },
    });

    if (!result && parent_bacteria?._id) {
      result = await IntrinsicResistance.findOne({
        bacteriaId: parent_bacteria._id,
        resistance: { $regex: chineseAgent, $options: 'i' },
      });
    }

    if (!result && parent_bacteria?.parent_id) {
      result = await IntrinsicResistance.findOne({
        bacteriaId: parent_bacteria.parent_id,
        resistance: { $regex: chineseAgent, $options: 'i' },
      });
    }

    let remark = result ? result.resistance : ''; // Use resistance if found

    // Fetch CLSI comments
    let drugDeduction = await getCLSIComments(bacteriaId, selectedAgent);

    if (!drugDeduction.length && parent_bacteria?._id) {
      drugDeduction = await getCLSIComments(parent_bacteria._id, selectedAgent);
    }

    if (!drugDeduction.length && parent_bacteria?.parent_id) {
      drugDeduction = await getCLSIComments(parent_bacteria.parent_id, selectedAgent);
    }

    const processedDrugDeduction = drugDeduction
      ? drugDeduction.replace(/参考(?:通用)?注释\(\d+\)|[≥≤]\s*\d+/g, '').trim()
      : null;

    // Fetch breaking data
    // let breakingData = await Breaking.find({
    //   'Antimicrobial Agent': { $regex: selectedAgent, $options: 'i' },
    //   BacterId: bacteriaId,
    // });
    //  console.log(parent_bacteria)

    let query = {
      'Antimicrobial Agent': { $regex: selectedAgent, $options: 'i' },
      BacterId: bacteriaId, 
      $or: [
        { bacteriaNames: { $exists: false } },  
        {bacteriaNames: { $size: 0 }  },
        { bacteriaNames: { $in: [bacteriaName] } } ]
    };
    // console.log(`查询折点数据: ${JSON.stringify(query)}`); 
    let breakingData = await Breaking.find(query);
    if (!breakingData.length && parent_bacteria?._id) {
     query = {
      'Antimicrobial Agent': { $regex: selectedAgent, $options: 'i' },
      BacterId: parent_bacteria._id, 
      $or: [
        { bacteriaNames: { $exists: false } },  
        {bacteriaNames: { $size: 0 }  },
        { bacteriaNames: { $in: [bacteriaName] } } ]
    };
  
     breakingData = await Breaking.find(query);
   }

   if (!breakingData.length && parent_bacteria?.parent_id) {
    query = {
     'Antimicrobial Agent': { $regex: selectedAgent, $options: 'i' },
     BacterId: parent_bacteria.parent_id, 
     $or: [
       { bacteriaNames: { $exists: false } },  
       {bacteriaNames: { $size: 0 }  },
       { bacteriaNames: { $in: [bacteriaName] } } ]
   };
 
    breakingData = await Breaking.find(query);
  }
  
    if (!breakingData.length) {
      remark = `报告缺药:没有发现${selectedAgent} 的折点数据`;
    }
   

    const breakingResults = await Promise.all(
      breakingData
        .filter((record) => {
          // Check if MIC values or Disk values are valid
          const isInvalidValue = (value) => !value || value === '-';
    
          // If selectedMethod is 'MIC', check if MIC values are valid
          if (selectedMethod === 'MIC') {
            const sRange = parseRange(record['S (μg/mL)']);
            const iRange = parseRange(record['I (μg/mL)']);
            const rRange = parseRange(record['R (μg/mL)']);
            
            // If any required MIC field is invalid, exclude this record
            if (isInvalidValue(record['S (μg/mL)']) && isInvalidValue(record['I (μg/mL)']) && isInvalidValue(record['R (μg/mL)'])) {
              return false; // Invalid MIC record, filter out
            }
          }
          
          // If selectedMethod is 'Disk', check if Disk values are valid
          if (selectedMethod === 'Disk') {
            const s_mm = parseRange(record['S (mm)']);
            const i_mm = parseRange(record['I (mm)']);
            const r_mm = parseRange(record['R (mm)']);
              if(isInvalidValue(record['Disk Content']))
                return false;
              // If any required Disk field is invalid, exclude this record
              if (isInvalidValue(record['S (mm)']) && isInvalidValue(record['I (mm)']) && isInvalidValue(record['R (mm)'])) {
                return false; // Invalid Disk record, filter out
              }
          }

           if (selectedMethod === 'MIC+Disk'){
              if(isInvalidValue(record['Disk Content']))
                return false;
              if (isInvalidValue(record['S (μg/mL)']) && isInvalidValue(record['I (μg/mL)']) && isInvalidValue(record['R (μg/mL)'])) {
                return false; // Invalid MIC record, filter out
              }
              if (isInvalidValue(record['S (mm)']) && isInvalidValue(record['I (mm)']) && isInvalidValue(record['R (mm)'])) {
                return false; // Invalid Disk record, filter out
              }

          }
    
          // Return true if the record is valid after the checks
          return true;
        })
        .map(async (record) => {
          let sensitivity = 'R';
    
          // Logic for 'MIC' method
          if (selectedMethod === 'MIC') {
            const sRange = parseRange(record['S (μg/mL)']);
            const iRange = parseRange(record['I (μg/mL)']);
            const rRange = parseRange(record['R (μg/mL)']);
           

            if ((sRange?.type === 'max'|| sRange?.type === 'exact') && micValue <= sRange.value) {
              sensitivity = 'S';
            } else if ((iRange?.type === 'max' || iRange?.type === 'exact') && micValue <= iRange.value) {
              sensitivity = 'I';
            } else if ((rRange?.type === 'min' || rRange?.type === 'exact') && micValue >= rRange.value) {
              sensitivity = 'R';
            }
            
          } 
        

          // Logic for 'Disk' method
          else if (selectedMethod === 'Disk') {
            const s_mm = parseRange(record['S (mm)']);
            const i_mm = parseRange(record['I (mm)']);
            const r_mm = parseRange(record['R (mm)']);
    
    // Check if the disk values are valid
            if (s_mm?.type === 'max' && diskContent >= s_mm.value) {
              sensitivity = 'S';
            } else if (i_mm?.type === 'range' && diskContent >= i_mm.min && diskContent <= i_mm.max) {
              sensitivity = 'I';
            } else if (r_mm?.type === 'min' && diskContent <= r_mm.value) {
              sensitivity = 'R';
            }
          }
          // Logic for 'MIC+Disk' method
          else if (selectedMethod === 'MIC+Disk') {
            const sRange = parseRange(record['S (μg/mL)']);
            const rRange = parseRange(record['R (μg/mL)']);
            const s_mm = parseRange(record['S (mm)']);
            const r_mm = parseRange(record['R (mm)']);
    
    // Check combined MIC+Disk conditions
            if (sRange?.type === 'max' && micValue <= sRange.value && diskContent >= s_mm?.value) {
              sensitivity = 'S';
            } else if (rRange?.type === 'min' && micValue >= rRange.value && diskContent <= r_mm?.value) {
              sensitivity = 'R';
            }
          }
    
          // Final check for '耐药' in remark
          if (remark.includes('耐药')) {
            sensitivity = 'R';
          }
    
          // Check additional sense condition
          if (sense && sense === 'R') {
            sensitivity = 'R';
          }
    
          return {
            bacteriaName,
            selectedAgent,
            micValue,
            diskContent,
            sensitivity,
            selectedMethod,
            expertics,
            'Disk Content': record['Disk Content'],
            'S (mm)': record['S (mm)'],
            'SDD (mm)': record['SDD (mm)'],
            'I (mm)': record['I (mm)'],
            'R (mm)': record['R (mm)'],
            'S (μg/mL)': record['S (μg/mL)'],
            'SDD (μg/mL)': record['SDD (μg/mL)'],
            'I (μg/mL)': record['I (μg/mL)'],
            'R (μg/mL)': record['R (μg/mL)'],
            comments: (() => {
              if (typeof record.Comments !== 'string' || record.Comments.trim() === "") return "";
            
              const match = record.Comments.match(/[:：]/);
              if (!match) return record.Comments;
            
              const colonIndex = match.index;
              const colonChar = match[0]; // ":" or "："
              const afterColon = record.Comments.slice(colonIndex + 1).trim();
              if (!afterColon) return record.Comments; // 冒号后没内容，就不插入

            
              return (
                record.Comments.slice(0, colonIndex + 1) +
                ` ${selectedAgent}, ` +
                record.Comments.slice(colonIndex + 1)
              );
            })()
            
          
          
          };
        })
    );
     
    if (!breakingResults.length) {
      remark = `报告缺药:没有发现${selectedAgent} 的折点数据`;
    }
    return {
      bacteriaName,
      selectedAgent,
      micValue,
      diskContent,
      selectedMethod,
      remark,
      processedDrugDeduction,
      drugInterpretation: '',
      breaking: breakingResults,
    };
  } catch (error) {
    console.error('Error processing breaking data:', error);
    throw new Error('Internal server error');
  }
};



const processRemark=async(micData)=>{
  const result = await IntrinsicResistance.findOne({
    'bateria': micData.bacteriaName,
    'resistance': { $regex: micData.selectedAgent, $options: 'i' } // Case-insensitive match
});

const remark = result ? result.resistance : '';
if(remark){
  await PatientMic.findByIdAndUpdate(
    micData._id,
    { remark: remark },
    { new: true } // Return the updated document
  );
 }
return remark;
}

app.get('/api', (req, res) => {
  res.json({ message: 'API is working!' });
});

app.get('/get-expertics', async (req, res) => {
  // Get the query parameters from the request
  const { chnBacteria, chnAntics } = req.query;

  if (!chnBacteria || !chnAntics) {
    return res.status(400).json({ error: 'Please provide both chnBacteria and chnAntics query parameters' });
  }

  // Call the getExpertics function
  const expertics = await getExpertics(chnBacteria, chnAntics);

  if (expertics) {
    return res.json(expertics);
  } else {
    return res.status(404).json({ message: 'No expertics found for the given bacteria and antibiotic' });
  }
});


// **Create a new SpecimenData**
app.post('/specimen', async (req, res) => {
  const { SPEC_NUM, ...restData } = req.body; // 提取 patient_id 和其他数据
 
  try {
    const updatedPatient = await SpecimenData.findOneAndUpdate(
      { SPEC_NUM }, // 查询条件：patient_id
      { $set: restData }, // 更新数据
      { new: true, upsert: true, setDefaultsOnInsert: true } // 如果不存在则插入
    );
    res.status(200).json({ message: '保存成功', data: updatedPatient });
  } catch (error) {
    console.error('Error saving patient:', error);
    res.status(500).json({ error: '保存失败' });
  }
});


// **Delete SpecimenData by SPEC_NUM**
app.delete('/specimen/:specNum', async (req, res) => {
  try {
    const specimen = await SpecimenData.findOneAndDelete({ SPEC_NUM: req.params.specNum });
    if (!specimen) {
      return res.status(404).json({ message: 'Specimen not found' });
    }
    res.status(200).json({ message: 'Specimen deleted' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// **Search SpecimenData by SPEC_NUM**
app.get('/specimen/:specNum', async (req, res) => {
  try {
    
    const specimen = await SpecimenData.findOne({ SPEC_NUM: req.params.specNum });
   
    if (!specimen) {
      return res.status(404).json({ message: 'Specimen not found' });
    }
    res.status(200).json(specimen);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});







// 1. 保存或更新患者数据
app.post('/patients', async (req, res) => {
  const { patient_id, ...restData } = req.body; // 提取 patient_id 和其他数据
 
  try {
    const updatedPatient = await Patient.findOneAndUpdate(
      { patient_id }, // 查询条件：patient_id
      { $set: restData }, // 更新数据
      { new: true, upsert: true, setDefaultsOnInsert: true } // 如果不存在则插入
    );
    res.status(200).json({ message: '保存成功', data: updatedPatient });
  } catch (error) {
    console.error('Error saving patient:', error);
    res.status(500).json({ error: '保存失败' });
  }
});

// 2. 通过 patient_id 获取患者信息
app.get('/api/patients/:specimenNumber', async (req, res) => {
  const { specimenNumber } = req.params;
 
  try {
    const patient = await Patient.findOne({ specimenNumber });
    console.log(patient);
    if (!patient) {
      return res.status(404).json({ message: '未找到该患者信息' });
    }
    res.status(200).json({ data: patient });
  } catch (error) {
    console.error('Error fetching patient:', error);
    res.status(500).json({ error: '获取患者信息失败' });
  }
});

app.get('/api/patients/consumable/:consumableboardNumber', async (req, res) => {
  const { consumableboardNumber } = req.params;
  // console.log(consumableboardNumber)
  try {
    const patient = await Patient.findOne({ consumableboardNumber });
     
    if (!patient) {
      return res.status(404).json({ message: '未找到该患者信息' });
    }
    res.status(200).json({ data: patient });
  } catch (error) {
    console.error('Error fetching patient:', error);
    res.status(500).json({ error: '获取患者信息失败' });
  }
});


// 3. 通过 patient_id 删除患者信息
app.delete('/api/patients/:patient_id', async (req, res) => {
  const { patient_id } = req.params;
  try {
    const deletedPatient = await Patient.findOneAndDelete({ patient_id });
    if (!deletedPatient) {
      return res.status(404).json({ message: '未找到要删除的患者信息' });
    }
    res.status(200).json({ message: '患者信息已删除', data: deletedPatient });
  } catch (error) {
    console.error('Error deleting patient:', error);
    res.status(500).json({ error: '删除失败' });
  }
});



//mic 相关
app.get('/api/mic-distribution', async (req, res) => {
  const { bacteria } = req.query;

  if (!bacteria) {
    return res.status(400).json({ error: 'Missing bacteria parameter' });
  }

  try {
    const results = await MicDistribution.find({ bacteria });
  
    const response = results.map(r => ({
      antibiotic: r.antibiotic,
      min: r.min,
      max: r.max,
      distribution: r.distribution
    }));
    res.json(response);
  } catch (err) {
    console.error('Error querying MIC distributions:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


app.get('/api/patients/:patient_id/mic-records', async (req, res) => {
  const { patient_id } = req.params;  
  
  try {
    let remark='';
    const micRecords = await PatientMic.find({ patient_id });

    if (micRecords.length > 0) {
      if(!micRecords[micRecords.length-1].remark)
         remark = await processRemark(micRecords[micRecords.length-1]);
      res.status(200).json(micRecords);
    } else {
      res.status(404).json({ message: '没有找到相关的 MIC 记录' });
    }
  } catch (error) {
    console.error('Error fetching MIC records:', error);
    res.status(500).json({ error: '查询失败' });
  }
});

app.delete('/api/patients/:patient_id/mic-reocrds',async(req,res)=>{
  try{
    const { patient_id } = req.params;
    
    await PatientMic.deleteMany({ patient_id });
    
    res.json({ message: '患者mic相关记录已删除' });
  }catch(error){
    console.error('Error deleting patient:', error);
    res.status(500).json({ message: '删除患者失败', error });
  }
});


app.post('/api/patients/sample', async (req, res) => {
  const { smartId, patient_id, originalAgent, secondAgent, result } = req.body;

  try {
    let sample;
   
    if (smartId) {
      // 如果存在 smartId，则更新记录
     
      sample = await PatientSample.findOneAndUpdate(
        { _id: smartId }, // 根据 smartId 查找记录
        {
          patient_id,
          originalAgent,
          secondAgent,
          result,
          
        },
        { new: true, upsert: true } // `new: true` 返回更新后的文档；`upsert: true` 如果找不到则创建
      );
      if (!sample) {
        return res.status(404).json({ message: '记录未找到，无法更新' });
      }
    } else {
      // 如果不存在 smartId，则创建新记录
    
      sample = new PatientSample({
        patient_id,
        originalAgent,
        secondAgent,
        result,
      });

      await sample.save();
    }

    res.status(201).json(sample);
  } catch (error) {
    console.error('Error saving sample:', error);
    res.status(500).json({ message: '操作失败' });
  }
});


// 通过 patient_id 查询样本记录 (Read)
app.get('/api/patients/sample/:patient_id', async (req, res) => {
  const { patient_id } = req.params;

  try {
    const sampleRecords = await PatientSample.find({ patient_id });
    
    if (sampleRecords.length > 0) {
      res.status(200).json(sampleRecords);
    } else {
      res.status(404).json({ message: '没有找到相关的样本记录' });
    }
  } catch (error) {
    console.error('Error fetching sample records:', error);
    res.status(500).json({ message: '查询失败' });
  }
});

// 更新样本记录 (Update)
app.put('/api/patients/sample/:id', async (req, res) => {
  const { id } = req.params;
  const { SmearResult, TestMethod, TrainResult, Remark } = req.body;

  try {
    const updatedSample = await PatientSample.findByIdAndUpdate(
      id,
      {
        SmearResult,
        TestMethod,
        TrainResult,
        Remark,
        update_time: Date.now()
      },
      { new: true }  // 返回更新后的记录
    );

    if (!updatedSample) {
      return res.status(404).json({ message: '样本记录未找到' });
    }

    res.status(200).json(updatedSample);
  } catch (error) {
    console.error('Error updating sample:', error);
    res.status(500).json({ message: '更新失败' });
  }
});



// 删除样本记录 (Delete)
app.delete('/api/patients/sample/:id', async (req, res) => {
  const { id } = req.params;

  try {
    const deletedSample = await PatientSample.findByIdAndDelete(id);

    if (!deletedSample) {
      return res.status(404).json({ message: '样本记录未找到' });
    }

    res.status(200).json({ message: '样本记录已删除' });
  } catch (error) {
    console.error('Error deleting sample:', error);
    res.status(500).json({ message: '删除失败' });
  }
});
app.delete('/api/patients/:patient_id/sample-records', async (req, res) => {
  const { patient_id } = req.params;
  const { smartId } = req.body; // 从请求体中获取 smartId
 
  try {
    if (smartId) {
      // 如果 smartId 存在，删除对应记录
      const deletedSample = await PatientSample.findByIdAndDelete(smartId);
      
      if (!deletedSample) {
        return res.status(404).json({ message: '未找到指定的样本记录' });
      }

      res.status(200).json({ message: '指定样本记录已删除', deletedSample });
    } else {
      // 如果 smartId 不存在，删除所有与 patient_id 相关的记录
      const result = await PatientSample.deleteMany({ patient_id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ message: '未找到与该患者 ID 相关的样本记录' });
      }

      res.status(200).json({ message: '患者所有样本记录已删除', deletedCount: result.deletedCount });
    }
  } catch (error) {
    console.error('Error deleting sample:', error);
    res.status(500).json({ message: '删除失败' });
  }
});


// 获取单个 sample 的详细信息
app.get('/api/patients/sample/detail/:id', async (req, res) => {
  const { id } = req.params;
 
  try {
    const sample = await PatientSample.findById(id);
    if (sample) {
      res.status(200).json(sample);
    } else {
      res.status(404).json({ message: '样本记录未找到' });
    }
  } catch (error) {
    console.error('获取样本详细信息失败:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

app.get('/api/patient/report/:patient_id', async (req, res) => {
  const { patient_id } = req.params;

  try {
    // 1. 查询 Patient 信息
    const patient = await Patient.findOne({ patient_id });
    if (!patient) {
      return res.status(404).json({ message: '未找到患者信息' });
    }

    // 2. 查询 PatientMic 信息
    const micRecords = await PatientMic.aggregate([
      { $match: { patient_id } }, // 匹配 patient_id
      { $group: { _id: "$selectedAgent", record: { $first: "$$ROOT" } } }, // 按 selectedAgent 分组，取第一条记录
      { $replaceRoot: { newRoot: "$record" } } // 返回完整的记录
    ]);
    

    // 3. 查询 PatientSample 信息
    const sampleRecords = await PatientSample.find({ patient_id })
      .sort({ create_time: -1 })  // 按创建时间降序排序
      .limit(1);       

    // 4. 合并结果生成报告
    const report = {
      patient,        // 患者的基本信息
      micRecords,     // MIC 记录
      sampleRecords,  // 样本记录
    };

    res.status(200).json(report);

  } catch (error) {
    console.error('获取患者信息时出错:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});


app.get('/api/suggestions', async (req, res) => {
  const query = req.query.query;
  let displayName=null;
  try {
    if(query.length==3){
      const codeSuggestions = await organismSchema.findOne({'ORGANISM_CODE' : query});
      if(codeSuggestions){
        displayName=codeSuggestions.DISPLAY_NAME;
      }
    }
    
    const suggestions = await BacterialTaxonomy.find({
      'name': { $regex: displayName==null?query:displayName, $options: 'i' }
      
    });
   
   
    res.json(
      suggestions.map(doc => ({
        bacteriaId:doc._id,
        name: doc.name,
        level: doc.level,
        parent_id: doc.parent_id
      }))
    );
  } catch (error) {
    console.error('Error fetching suggestions:', error);
    res.status(500).json({ error: 'Server error' });
  }
});



app.get('/api/getBacteriaByAntibioticCode/:antiCode', async (req, res) => {
  const { antiCode } = req.params;
  let displayName = null;
  if(antiCode.length==3){
    try {
   
      // 1. Find the antibiotic's display name using the antiCode
      const codeSuggestion = await Antibiotics.findOne({ 'ANTI_CODE': antiCode });
      //  console.log("codeSugestion",codeSuggestion)
      if (codeSuggestion) {
        displayName = codeSuggestion.DISPLAY_NAME;
  
        // 2. Find all BacterId in Breaking where 'Antimicrobial Agent' contains displayName
        const breakingRecords = await Breaking.find({ 'Antimicrobial Agent': { $regex: displayName, $options: 'i' } });
        // console.log("breakingRecords",breakingRecords)
        if (breakingRecords.length > 0) {
          // Collect all unique BacterIds from the breaking records
          const uniqueBacterIds = [...new Set(breakingRecords.map(record => record.BacterId))];
  
          // 3. Find all child bacteria from BacterialTaxonomy using BacterId
          const bacteria = await BacterialTaxonomy.find({ parent_id: { $in: uniqueBacterIds } });
  
          let fullBacteriaData = [];
          
          if (bacteria.length > 0) {
           
            const bacteriaIds = bacteria.map((b) => b._id);
          
            // Fetch children data for the retrieved bacteria
            const childrenData = await BacterialTaxonomy.find({ parent_id: { $in: bacteriaIds } });
            // console.log("childrendData",childrenData);
            // Combine bacteria data with their children
            fullBacteriaData = bacteria.map((b) => {
              const children = childrenData.filter((child) => child.parent_id.toString() === b._id.toString());
              return {
                ...b.toObject(), // Convert Mongoose document to plain object
                children,
              };
            });
          }
          
  
          // 4. Return the matched bacteria
          if (fullBacteriaData.length > 0) {
            return res.status(200).json(fullBacteriaData);
          } else {
            return res.status(404).json({ message: 'No child bacteria found for the given antibiotic code.' });
          }
        } else {
          return res.status(404).json({ message: 'No matching BacterId found in Breaking records.' });
        }
      } else {
        return res.status(404).json({ message: 'No matching antibiotic code found.' });
      }
    } catch (error) {
      console.error('Error fetching bacteria by antibiotic code:', error);
      return res.status(500).json({ message: 'Server error while fetching data.', error });
    }
  }
  
});

app.get('/api/getAgents', async (req, res) => {
  const { bacteriaId } = req.query;

  try {
    // Fetch parent bacteria ID
    const parentBacteria = await getLevelId(bacteriaId);
    const parentBacteriaId = parentBacteria?._id;
    const grandParentBacteriaId=parentBacteria?.parent_id; 
    // Fetch agents for the current bacteria or its parent
    const parentAgents = parentBacteriaId
      ? await Breaking.distinct('Antimicrobial Agent', { BacterId: parentBacteriaId })
      : [];
    const agents = await Breaking.distinct('Antimicrobial Agent', { BacterId: bacteriaId });
    let combinedAgents = agents.length > 0 ? agents : parentAgents;
    if(combinedAgents.length==0){
      combinedAgents = grandParentBacteriaId
      ? await Breaking.distinct('Antimicrobial Agent', { BacterId: grandParentBacteriaId })
      : []; 
    }
    
    // Remove all English portions, spaces, and unnecessary symbols from agent names
    const cleanedAgents = Array.from(new Set(
      combinedAgents
        .map(agent => agent.replace(/[a-zA-Z\s().\-]/g, '').trim()) // 去除英文、空格、符号
        .filter(agent => agent.length > 0) // 去除空字符串
    ));
    

    // Reorder agents to prioritize recent selections
    const orderedAgents = [
      ...recent_agents.filter(agent => cleanedAgents.includes(agent)),
      ...cleanedAgents.filter(agent => !recent_agents.includes(agent)),
    ];

    // Respond with the cleaned agents
    res.json({ agents: orderedAgents, source: agents.length > 0 ? 'current' : 'parent' });
  } catch (error) {
    console.error('Error fetching agents:', error); // Log the error
    res.status(500).json({ error: 'Server error' }); // Return a 500 status with an error message
  }
});



app.get('/api/comment-details', async (req, res) => {
  const query = req.query.query;
  try {
    const result = await Breaking.find({ 'Antimicrobial Agent': new RegExp(`\\(${query}\\)`) });
    res.json(result);
  } catch (error) {
    res.status(500).send('Error fetching comment details');
  }
});


app.post('/api/query', async (req, res) => {
  const { patient_id, bacteriaName,selectedAgent, micValue} = req.body;
  let sensitivity='R';
  let bactera_id='';
  let remark='';


  try {
    // 查询数据库
    const selectedBacteria = await BacterialTaxonomy.findOne({'name' : bacteriaName});
    if(selectedBacteria){
      if(selectedBacteria.level == 1)
         bactera_id=selectedBacteria._id;
      else if(selectedBacteria.level==2)
         bactera_id = selectedBacteria.parent_id;
      else
         {
          ancestor = await getLevelId(selectedBacteria.parent_id)
          if(ancestor)
           bactera_id=ancestor.parent_id;
         }
    }

      
    const breakingData = await Breaking.findOne({ 'Antimicrobial Agent' : selectedAgent,'BacterId':bactera_id });
    const tableBData = await TableB.findOne({'bateria':bacteriaName,'agent':selectedAgent});
    const tableAData = await TableA.findOne({'Organism': { $regex: bacteriaName, $options: 'i' }});
   
    if(breakingData){
      const sRange = parseRange(breakingData['S (μg/mL)']);
      const iRange = parseRange(breakingData['I (μg/mL)']);
      const rRange = parseRange(breakingData['R (μg/mL)']);
      if (sRange && sRange.type === 'max' && micValue <= sRange.value) {
        sensitivity = 'S';
       
      } else if (
        iRange &&
        iRange.type === 'max' &&
        micValue <= iRange.value
      ) {
        sensitivity = 'I';
      } else if (
        rRange &&
        rRange.type === 'min' &&
        micValue >= rRange.value
      ) {
        sensitivity = 'R';
       
      }
    }
   
   if(tableAData){
    if(tableAData.CatI){
      remark='罕见耐药:I类 未曾报告过或仅少见报告';
    }
    else if(tableAData.CatII){
     remark='罕见耐药:II类 在大多数机构不常见';
    }
    else if(tableAData.CatIII){
      remark='罕见耐药:III类 可能常见，但通常与流行病学有关';
    }
   }

   if(tableBData){
      if(tableBData.Resist=='R'){
        sensitivity= tableBData.Resist;
        remark += " " + bacteriaName + "对" + selectedAgent + "天然耐药";
      }
      
   }
   
 
   if (Carbapenem.includes(selectedAgent)) {
     
     remark += " 提示该菌为CRE,需加强院感管理,防止院内传播"
   } 


   const newPatientMicRecord = new PatientMic({
    patient_id,
    bacteriaName,
    selectedAgent,
    micValue,
    sensitivity,
    'S (μg/mL)': breakingData ? breakingData['S (μg/mL)'] : null,
    'SDD (μg/mL)': breakingData ? breakingData['SDD (μg/mL)'] : null,
    'I (μg/mL)': breakingData ? breakingData['I (μg/mL)'] : null,
    'R (μg/mL)': breakingData ? breakingData['R (μg/mL)'] : null,
    remark
  });

  await newPatientMicRecord.save(); 


    res.json({
      bacteriaName,
      selectedAgent,
      micValue,
      sensitivity,
      remark,
      breaking: breakingData,
    
    });
  } catch (error) {
    console.error('Error querying database:', error);
    res.status(500).json({ error: '查询失败' });
  }
});


// Create or Update Breaking
app.post('/api/mic/addorup', async (req, res) => {
const data = req.body;
const ancestor = await getLevelId(data.BacterId);
if (ancestor) {
     if(ancestor.parent_id)
      data.BacterId = ancestor.parent_id;
  } else {
        return res.status(404).json({ error: 'Ancestor not found' });
}
  const currentBacteria = await BacterialTaxonomy.findOne({ _id: new mongoose.Types.ObjectId(data.OrignalBacterId) });

 try {
     
    const existingRecord = await Breaking.findOne({ newId: data.newId });

    if (existingRecord) {
      // 存在记录，执行更新
      const updatedBreaking = await Breaking.findOneAndUpdate(
        { newId: data.newId }, // 条件
        { ...data, update_date: Date.now() }, // 更新字段，设置 update_date
        { new: true } // 返回更新后的记录
      );
      return res.status(200).json({ message: 'Record updated', data: updatedBreaking,datab:currentBacteria });
    } else {
      // 不存在记录，执行插入
    
      const newBreaking = new Breaking({
        ...data,
        create_date: Date.now(), // 设置 create_date
        update_date: Date.now(), // 初始值与 create_date 一致
      });
      const savedBreaking = await newBreaking.save();
      return res.status(201).json({ message: 'Record created', data: savedBreaking,datab:currentBacteria });
    }
  } catch (error) {
    console.error('Error in Breaking API:', error);
    res.status(500).json({ message: 'Internal server error', error });
  }
});


app.get('/api/mic/:newId/breaking', async (req, res) => {
  const { newId } = req.params;
  
  try {
      const breaking = await Breaking.findOne({ newId });

    if (!breaking) {
     
      return res.status(404).json({ message: 'Record not found' });
    }
     res.status(200).json({ message: 'Record found', data: breaking });
  } catch (error) {
    console.error('Error finding Breaking by newId:', error);
    res.status(500).json({ message: 'Internal server error', error });
  }
});


app.get('/api/bacteria/:bacterId', async (req, res) => {
  const { bacterId } = req.params;
 
  try {
    // Add await to actually execute the query and wait for the result
    const bacteria = await BacterialTaxonomy.findOne({ _id: new mongoose.Types.ObjectId(bacterId) });

    if (!bacteria) {
      return res.status(404).json({ message: 'Record not found' });
    }

    res.status(200).json({ message: 'Record found', data: bacteria });
  } catch (error) {
    console.error('Error finding by id:', error);
    
    // More specific error handling
    if (error.name === 'CastError') {
      return res.status(400).json({ message: 'Invalid bacteria ID format' });
    }

    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
});




app.delete('/api/mic/:newId', async (req, res) => {
  const { newId } = req.params;
  try {
      const mic = await Breaking.findOneAndDelete({ newId });
    if (!mic) {
      return res.status(404).json({ message: '未找到mic记录' });
    }
    res.json({ message: 'mic记录已删除' });
  } catch (error) {
    console.error('Error deleting mic error');
    res.status(500).json({ message: '删除mic失败', error });
  }
});

app.get('/api/breaking-interpret', async (req, res) => {
  const { selectedAgent, micValue, bacteriaId, bacteriaName } = req.query;

    if (!selectedAgent || !micValue || !bacteriaId ) {
    return res.status(400).json({ error: 'selectedAgent, micValue, bacteriaId, and selectedMethod are required' });
  }

  try {
    // Assemble data object for processMicData
    // 确保micValue是数字类型
    const numericMicValue = parseFloat(micValue.toString().replace('≤', '').replace('≥', '').replace('>', '').replace('<', ''));
 
    const data = {
      bacteriaId,
      bacteriaName: bacteriaName || '',
      selectedAgent,
      micValue: numericMicValue,
      diskContent: null, // Assuming diskContent is not needed for this endpoint'',
      selectedMethod:  'MIC',
    };
    // Call processMicData and extract sensitivity from breaking array
    const result = await processMicData(data);
    // console.log('🔍 processMicData完整结果:', JSON.stringify(result, null, 2));

    let sensitivity = null;
    let remark = '';
    let comments = '';
    let breakingData = null;

    if (result.breaking && Array.isArray(result.breaking) && result.breaking.length > 0) {
      const breakingResult = result.breaking[0];
      sensitivity = breakingResult.sensitivity;
      breakingData = breakingResult;

      // 获取comments - 从breaking结果中获取
      if (breakingResult.comments) {
        comments = breakingResult.comments;
      }

      // console.log('✅ 从breaking数组获取数据:', { sensitivity, comments: comments ? '有' : '无' });
    }

    // 获取remark信息
    if (result.remark) {
      remark = result.remark;
      // console.log('✅ 获取remark:', remark);
    }

    // 获取comments - 优先从breaking结果，然后从processedDrugDeduction
    if (!comments && result.processedDrugDeduction) {
      comments = result.processedDrugDeduction;
      // console.log('✅ 从processedDrugDeduction获取comments:', comments);
    }

    // 如果还没有comments，尝试从drugDeduction获取
    if (!comments && result.drugDeduction) {
      comments = result.drugDeduction;
      console.log('✅ 从drugDeduction获取comments:', comments);
    }

    return res.json({
      sensitivity,
      remark,
      comments,
      breakingData: breakingData,
      expertics: result.expertics || null
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Internal server error' });
  }
});



app.post('/api/multimethods/breaking', async (req, res) => {
  const data = req.body;
 
  if (!Array.isArray(data) || data.length === 0) {
    return res.status(400).json({ error: 'Invalid input. Expecting a non-empty data array.' });
  }
  
  try {
    let processedResults=[];
    
    data.map((item)=>{
      if (item.selectedAgent && !recent_agents.includes(item.selectedAgent)) {
        recent_agents.unshift(item.selectedAgent);  // Add to the beginning
        if (recent_agents.length > 10) {
          recent_agents.pop();  // Limit the array size to the last 10 selections
        }
      }
    });
  
    
    // const result_deduct =data.length===1?null : await Bacteria.findOne({
    //   bacteria_name: { $regex: data[0].bacteriaName, $options: 'i' }, // Ensure bacteria_name matches
    //   antibiotics: {
    //     $all: data.map((agent) => ({ $elemMatch: { name: agent.selectedAgent } })),
    //   },
    // });
    const result_deduct = data.length === 1 ? null : await Bacteria.findOne({
      bacteria_name: { $regex: data[0].bacteriaName, $options: 'i' },  // Match bacteria_name with regex
      antibiotics: {
        $all: data.map((agent) => ({
          $elemMatch: { name: { $regex: agent.selectedAgent, $options: 'i' } } // Match selectedAgent name with regex
        }))
      }
    });
    
   
    
    if(!result_deduct){
      for (let i = 0; i < data.length; i++){
        const result= await processMicData(data[i]);
      
         if(result){
          const micValues = {};
          result.breaking.forEach(breakingRecord => {
            micValues[breakingRecord.selectedAgent] = parseFloat(breakingRecord.micValue) || null;
          });
  
          const esblAlert = checkESBLAlert(micValues);
          if (esblAlert) {
            result.ESBL_Alert = esblAlert;
          }
          processedResults.push(result)
         }
 
      }
      
      res.json(processedResults);
    }
    else{
       processedResults = await Promise.all(
        result_deduct.antibiotics.map(async (antibiotic) => {
          const agentData = data.find((item) => item.selectedAgent === antibiotic.name);
          if (agentData) {
            const result = await processMicData(agentData, antibiotic.result);
          
            // result.drugInterpretation = result_deduct.interpretation;
            return result;
          }
          return null;
        })
      );
      const filteredResults = processedResults.filter(result => result !== null);
      filteredResults[0].drugInterpretation = result_deduct.interpretation;
      res.json(filteredResults);

    }
  } catch (error) {
    console.error("Error processing request:", error);
    res.status(500).json({ error: "Internal server error" });
  }

});







async function buildTree(node) {
  
  const children = await BacterialTaxonomy.find({ _id: { $in: node.children } });
  const childTrees = await Promise.all(children.map(async (child) => {
    return await buildTree(child);
  }));

  return {
    _id: node._id,
    name: node.name,
    level: node.level,
    children: childTrees 
  };
}


// 获取细菌分类树的API
app.get('/api/taxonomy/tree', async (req, res) => {
  try {
    // 查找根节点（parent_id 为 null 的节点）
    const roots = await BacterialTaxonomy.find({ parent_id: null });
    
    // 对所有根节点进行递归，构建完整的树结构
    const taxonomyTree = await Promise.all(roots.map(async (root) => {
      return await buildTree(root);
    }));
    
    // 返回树结构
    res.json(taxonomyTree);
  } catch (error) {
    console.error('Error fetching taxonomy tree:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});




// 添加新节点
app.post('/api/taxonomy/addNode', async (req, res)=>{
  const { name, level, parent_id } = req.body;
 
  try {
    // 创建新节点
    const newNode = new BacterialTaxonomy({ name, level, parent_id });

    // 如果有 parent_id，则将新节点添加到父节点的 children 中
    if (parent_id) {
      const parentNode = await BacterialTaxonomy.findById(parent_id);
      if (!parentNode) {
        return res.status(404).json({ message: '父节点未找到' });
      }
      parentNode.children.push(newNode._id);
      await parentNode.save();
    }

    // 保存新节点
    await newNode.save();
    res.status(201).json({ message: '节点添加成功', node: newNode });
  } catch (error) {
    res.status(500).json({ message: '添加节点失败', error });
  }
});

app.put('/api/taxonomy/update', async (req, res) => {
  const { key, name } = req.body;
  if (!key || !name) {
    return res.status(400).json({ message: 'Key and name are required.' });
  }
  try {
    const updatedNode = await BacterialTaxonomy.findByIdAndUpdate(
      key,                      // Use `key` as the document's `_id`
      { name },                 // Update the `name` field
      { new: true }             // Return the updated document
    );
    if (!updatedNode) {
      return res.status(404).json({ message: 'Node not found.' });
    }
    res.status(200).json({ message: 'Node updated successfully.', node: updatedNode });
  } catch (error) {
    console.error('Error updating node:', error);
    res.status(500).json({ message: 'Failed to update node.', error });
  }
});


// 删除节点及其子节点
app.delete('/api/taxonomy/deleteNode/:id', async (req, res) => {
  const { id } = req.params;  // 获取请求中的 id
  try {
    const node = await BacterialTaxonomy.findById(id);
    if (!node) {
      return res.status(404).json({ message: '节点未找到' });
    }

    // 递归删除子节点
    const deleteChildren = async (node) => {
      for (let childId of node.children) {
        const childNode = await BacterialTaxonomy.findById(childId);
        if (childNode) {
          await deleteChildren(childNode); // 递归删除子节点
          await BacterialTaxonomy.deleteOne({ _id: childNode._id }); // 删除子节点
        }
      }
    };

    await deleteChildren(node);

    // 如果有父节点，移除父节点中的引用
    if (node.parent_id) {
      const parentNode = await BacterialTaxonomy.findById(node.parent_id);
      if (parentNode) {
        parentNode.children.pull(id); // 移除子节点引用
        await parentNode.save();
      }
    }

    // 删除当前节点
    await BacterialTaxonomy.deleteOne({ _id: node._id });
    res.status(200).json({ message: '节点及其子节点已删除' });
  } catch (error) {
    console.error('删除节点失败:', error);
    res.status(500).json({ message: '删除节点失败', error });
  }
});


//获取whonet规则
app.get('/whonetRules', async (req, res) => {
  try {
    const antibiotics = await WhonetRules.find();
    res.json(antibiotics);
  } catch (err) {
    res.status(500).json({ error: '加载whonet数据失败' });
  }
});


app.get('/whonetRules/:ORG_CODES', async (req, res) => {
  const { ORG_CODES } = req.params;
 
  try {
      
      const antibiotic = await WhonetRules.find({ ORG_CODES });
      if (!antibiotic) return res.status(404).json({ message: '未找到该菌种' });
      res.json(antibiotic);
    } catch (err) {
      res.status(500).json({ error: '查询失败' });
    }
});



// 获取所有抗生素数据
app.get('/antibiotics', async (req, res) => {
  try {
    const antibiotics = await Antibiotics.find();
    res.json(antibiotics);
  } catch (err) {
    res.status(500).json({ error: '加载抗生素数据失败' });
  }
});


// 根据抗生素代码查询
app.get('/antibiotics/:ANTI_CODE', async (req, res) => {
  const { ANTI_CODE } = req.params;
 
  try {
      
      const antibiotic = await Antibiotics.findOne({ ANTI_CODE });
      if (!antibiotic) return res.status(404).json({ message: '未找到该抗生素' });
      res.json(antibiotic);
    } catch (err) {
      res.status(500).json({ error: '查询失败' });
    }
});

app.delete('/antibiotics/:ANTI_CODE', async (req, res) => {
  const { ANTI_CODE } = req.params;
  try {
    const antibiotic = await Antibiotics.findOneAndDelete({ ANTI_CODE });
    if (!antibiotic) return res.status(404).json({ message: '未找到该抗生素' });
    res.json(antibiotic);
  } catch (err) {
    res.status(500).json({ error: '查询失败' });
  }
});

// 保存或更新抗生素数据
app.post('/api/antibiotics', async (req, res) => {
  const data = req.body;
  
  try {
    const existingAntibiotic = await Antibiotics.findOne({ ANTI_CODE: data.ANTI_CODE });
    if (existingAntibiotic) {
      // 更新
      await Antibiotics.updateOne({ ANTI_CODE: data.ANTI_CODE }, data);
      return res.json({ message: '更新成功' });
    } else {
      // 保存新数据
      const newAntibiotic = new Antibiotics(data);
      await newAntibiotic.save();
      return res.json({ message: '保存成功' });
    }
  } catch (err) {
    res.status(500).json({ error: '保存失败' });
  }
});


app.post('/api/save-data', async (req, res) => {
  const { newId, data } = req.body;

  if (!newId || !Array.isArray(data) || data.length === 0) {
    return res.status(400).json({ message: 'Invalid input data' });
  }

  try {
    // Delete existing records with the same newId
    await Breaking.deleteMany({ newId });

    // Add the new data
    const newRecords = data.map(item => ({ ...item, newId })); // Add newId to each data entry
    await Breaking.insertMany(newRecords);

    res.status(200).json({ message: 'Data saved successfully' });
  } catch (error) {
    console.error('Error saving data:', error);
    res.status(500).json({ message: 'Failed to save data', error });
  }
});

app.get('/api/get-data/:newId', async (req, res) => {
  const { newId } = req.params;

  if (!newId) {
    return res.status(400).json({ message: 'newId is required' });
  }

  try {
    // Find records by newId
    const records = await Breaking.find({ newId });
   
    if (records.length === 0) {
      return res.status(404).json({ message: 'No data found for the provided newId' });
    }

    res.status(200).json({ data: records });
  } catch (error) {
    console.error('Error fetching data:', error);
    res.status(500).json({ message: 'Failed to fetch data', error });
  }
});


app.get('/api/organisms/code/:organismCode', async (req, res) => {
  try {
   
    const organism = await Organism.findOne({ ORGANISM_CODE: req.params.organismCode });
    
    if (!organism) {
      return res.status(404).json({ error: 'Organism not found' });
    }
    res.json(organism);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});


app.delete('/api/delete-data/:newId', async (req, res) => {
  const { newId } = req.params;

  if (!newId) {
    return res.status(400).json({ message: 'Invalid newId parameter' });
  }

  try {
    // Delete all records matching the provided newId
    const deleteResult = await Breaking.deleteMany({ newId });

    if (deleteResult.deletedCount === 0) {
      return res.status(404).json({ message: 'No records found to delete' });
    }

    res.status(200).json({ message: `Successfully deleted ${deleteResult.deletedCount} records` });
  } catch (error) {
    console.error('Error deleting data:', error);
    res.status(500).json({ message: 'Failed to delete data', error });
  }
});


// GET method: Retrieve English and Chinese names by code
app.get('/api/spec/:code', async (req, res) => {
  const { code } = req.params;

  try {
    // Query the collection for the given code
    const record = await TDictSpec.findOne({ code: code });

    if (!record) {
      return res.status(404).json({ message: 'Record not found for the provided code.' });
    }

    // Return the English and Chinese names
    res.status(200).json({
      code: record.code,
      englishName: record.englishname,
      chineseName: record.chinesename,
    });
  } catch (error) {
    console.error('Error fetching record:', error);
    res.status(500).json({ message: 'Error fetching record.', error });
  }
});

// Display all entries in TDictSpec
app.get('/api/tdictspec', async (req, res) => {
  try {
    const allEntries = await TDictSpec.find({});
    res.status(200).json(allEntries);
  } catch (error) {
    console.error('Error fetching all entries:', error);
    res.status(500).json({ message: 'Failed to fetch entries', error });
  }
});

// Search by code in TDictSpec
app.get('/api/tdictspec/search', async (req, res) => {
  const { code } = req.query;
   
  // Validate that the code is provided
  if (!code) {
    return res.status(400).json({ message: 'Code is required for search' });
  }

  // Validate that the code contains only English letters
  if (!/^[a-zA-Z]+$/.test(code)) {
    return res.status(400).json({ message: 'Code must contain only English letters' });
  }

  try {
    
    const entry = await TDictSpec.findOne({ code: code });
  
    if (entry) {
      res.status(200).json(entry);
    } else {
      res.status(404).json({ message: 'No entry found for the given code' });
    }
  } catch (error) {
    console.error('Error searching by code:', error);
    res.status(500).json({ message: 'Failed to search by code', error });
  }
});
app.post('/api/bacteria', async (req, res) => {
  const { interpretation_name, bacteria_name, specType,...restData } = req.body; // Extract data from the request body

  try {
    // Find and update the record by interpretation_name
    const updatedBacteria = await Bacteria.findOneAndUpdate(
      { interpretation_name }, // Query condition
      {
        $set: {
          bacteria_name,  // Directly set bacteria_name
          specType,
          ...restData      // Spread the remaining data into the update object
        }
      },
      { new: true, upsert: true, setDefaultsOnInsert: true } // Options to update or insert
    );
    
    res.status(200).json({ message: '保存成功', data: updatedBacteria });
  } catch (error) {
    console.error('Error saving interpreter:', error);
    res.status(500).json({ error: '保存失败' });
  }
});


app.get('/api/bacteria/detail/:id', async (req, res) => {
  const { id } = req.params;
 
  try {
    const sample = await Bacteria.findById(id);
    
    if (sample) {
      res.status(200).json(sample);
    } else {
      res.status(404).json({ message: '样本记录未找到' });
    }
  } catch (error) {
    console.error('获取样本详细信息失败:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

app.delete('/api/inter/:inter_id', async (req, res) => {
  const { sample_id } = req.params;
 
  try {
    const deletedPatient = await Bacteria.findOneAndDelete({ sample_id });
    if (!deletedPatient) {
      return res.status(404).json({ message: '未找到要删除的信息' });
    }
    res.status(200).json({ message: '信息已删除', data: deletedPatient });
  } catch (error) {
    console.error('Error deleting patient:', error);
    res.status(500).json({ error: '删除失败' });
  }
});


app.get('/api/bacteria', async (req, res) => {
  try {
    const { testMethod } = req.query;  // 获取请求中的 testMethod 参数

    let bacteriaData;
    if (testMethod) {
      // 如果提供了 testMethod 参数，则根据 testMethod 过滤，并按 update_date 降序排序
      bacteriaData = await Bacteria.find({
        testMethod: { $regex: testMethod, $options: 'i' } // 不区分大小写的部分匹配
      })
      .sort({ update_date: -1 });  // 按 update_date 降序排列
    } else {
      // 如果没有提供 testMethod 参数，则返回所有数据，并按 update_date 降序排序
      bacteriaData = await Bacteria.find().sort({ update_date: -1 });  // 按 update_date 降序排列
    }

    res.status(200).json(bacteriaData);  // 返回数据
  } catch (error) {
    console.error('Error fetching bacteria data:', error);
    res.status(400).json({ error: 'Failed to fetch bacteria data' });
  }
});



app.post('/api/bacteria/interpreter', async (req, res) => {
  const { selectedTestMethod, selectedSpecType } = req.body;
 
  try {
    const sanitizedMethod = selectedTestMethod.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(sanitizedMethod, 'i');
    
    const bacteriaData = await Bacteria.findOne({
      testMethod: { $regex: regex },  
      spec_type: selectedSpecType
    });
   
    if (bacteriaData) {
      res.status(200).json(bacteriaData);  // 返回匹配的数据
    } else {
      res.status(404).json({ message: 'No matching data found' });
    }
  } catch (error) {
    console.error('Error fetching bacteria data:', error);
    res.status(500).json({ message: 'Error fetching bacteria data' });
  }
});


// app.get('*', (req, res) => {
//   res.sendFile(path.join(__dirname, 'build', 'index.html'));
// });




// 获取所有患者信息的API端点
app.get('/api/samples', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sampleType,
      department,
      patientId,
      startDate,
      endDate
    } = req.query;

    // 构建查询条件
    const query = {};

    // 样本类型过滤
    if (sampleType) query.sampleType = sampleType;

    // 科室过滤
    if (department) query.department = department;

    // 患者编号过滤
    if (patientId) query.patient_id = patientId;

    // 日期范围过滤
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // 分页参数
    const skip = (page - 1) * limit;
    const limitNum = parseInt(limit);

    // 从patients集合查询数据
    const patients = await Patient.find(query)
      .skip(skip)
      .limit(limitNum)
      .sort({ createdAt: -1 });

    // 转换为样本格式，匹配前端期望的字段
    const samples = patients.map(patient => ({
      _id: patient._id,
      labId: patient.specimenNumber || patient.patient_id, // 标本编号
      bacteriaName: patient.cultureResult || '未知细菌', // 菌种名称
      sampleSource: patient.sampleType || '未知来源', // 样本来源
      patientId: patient.patient_id, // 患者编号
      patientName: patient.name, // 患者姓名
      department: patient.department, // 科室
      collectionDate: patient.createdAt ? new Date(patient.createdAt).toISOString().split('T')[0] : null,
      status: 'Completed', // 默认状态
      priority: 'Normal', // 默认优先级
      confidence: 95, // 默认置信度
      testDataList: patient.testDataList || [],
      // 原始患者数据
      originalPatientData: {
        age: patient.age,
        gender: patient.gender === '1' ? '男' : patient.gender === '0' ? '女' : patient.gender,
        bed: patient.bed,
        ward: patient.ward,
        medicalRecordNumber: patient.medicalRecordNumber,
        smearResult: patient.smearResult,
        consumableboardNumber: patient.consumableboardNumber,
        hospital_code: patient.hospital_code,
        remark: patient.remark,
        bacteriaCount: patient.bacteriaCount,
        bacteriaCode: patient.bacteriaCode,
        sampleCode: patient.sampleCode
      }
    }));

    // 获取总数
    const total = await Patient.countDocuments(query);

    res.json({
      success: true,
      data: samples,
      pagination: {
        page: parseInt(page),
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Error fetching patient samples:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching patient samples',
      error: error.message
    });
  }
});

// 创建新的患者样本记录
app.post('/api/samples', async (req, res) => {
  try {
    const {
      labId,
      bacteriaName,
      bacteriaCode,
      sampleSource,
      patientId,
      patientName,
      department,
      collectionDate,
      status,
      priority,
      age,
      gender,
      bed,
      ward,
      medicalRecordNumber,
      smearResult,
      remark
    } = req.body;

    // 验证必填字段
    if (!labId || !bacteriaName || !sampleSource || !patientId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: labId, bacteriaName, sampleSource, patientId'
      });
    }

    // 检查是否已存在相同的患者ID或标本编号
    const existingPatient = await Patient.findOne({
      $or: [
        { specimenNumber: labId },
        { patient_id: patientId }
      ]
    });

    if (existingPatient) {
      return res.status(409).json({
        success: false,
        message: 'Patient with this specimen number or patient ID already exists'
      });
    }

    // 创建新的患者记录
    const newPatient = new Patient({
      patient_id: patientId,
      specimenNumber: labId,
      name: patientName || 'Unknown',
      age: age || '0',
      gender: gender || '男',
      department: department || 'Lab',
      sampleType: sampleSource,
      cultureResult: bacteriaName,
      bed: bed || '',
      ward: ward || '',
      medicalRecordNumber: medicalRecordNumber || '',
      smearResult: smearResult || '',
      remark: remark || '',
      hospital_code: '123456', // 默认医院代码
      consumableboardNumber: Math.floor(Math.random() * 9999).toString(),
      sampleCode: sampleSource === '血液' ? 'bl' :
                  sampleSource === '尿液' ? 'ur' :
                  sampleSource === '脑脊液' ? 'csf' :
                  sampleSource === 'Blood' ? 'bl' :
                  sampleSource === 'Urine' ? 'ur' :
                  sampleSource === 'CSF' ? 'csf' : 'ot',
      testDataList: []
    });

    const savedPatient = await newPatient.save();

    // 转换为样本格式返回
    const sample = {
      _id: savedPatient._id,
      labId: savedPatient.specimenNumber,
      bacteriaName: savedPatient.cultureResult,
      sampleSource: savedPatient.sampleType,
      patientId: savedPatient.patient_id,
      patientName: savedPatient.name,
      department: savedPatient.department,
      collectionDate: savedPatient.createdAt ? new Date(savedPatient.createdAt).toISOString().split('T')[0] : null,
      status: 'Completed',
      priority: priority || 'Normal',
      confidence: 95,
      testDataList: savedPatient.testDataList,
      originalPatientData: {
        age: savedPatient.age,
        gender: savedPatient.gender,
        bed: savedPatient.bed,
        ward: savedPatient.ward,
        medicalRecordNumber: savedPatient.medicalRecordNumber,
        smearResult: savedPatient.smearResult,
        consumableboardNumber: savedPatient.consumableboardNumber,
        hospital_code: savedPatient.hospital_code,
        remark: savedPatient.remark
      }
    };

    res.status(201).json({
      success: true,
      data: sample,
      message: 'Patient sample created successfully'
    });
  } catch (error) {
    console.error('Error creating patient sample:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating patient sample',
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
module.exports = app;
// 获取所有菌株信息
app.get('/api/organisms', async (req, res) => {
  try {
    const organisms = await Organism.find({}, { _id: 0, ORGANISM_CODE: 1, DISPLAY_NAME: 1 });
    res.status(200).json(organisms);
  } catch (error) {
    console.error('Error fetching organisms:', error);
    res.status(500).json({ message: 'Failed to fetch organisms', error });
  }
});

// TableA查询API - 根据微生物群组和耐药级别查询
app.get('/api/tableA', async (req, res) => {
  try {
    const { microorganismGroup, resistanceClasses } = req.query;
    // 构建查询条件
    const query = {};

    // 微生物群组过滤
    if (microorganismGroup) {
      // 支持模糊匹配和精确匹配
      query.$or = [
        { microorganism_group: microorganismGroup },
        { microorganism_group: { $regex: microorganismGroup, $options: 'i' } },
        { microorganism_group: '任何肠杆菌目细菌' } // 通用规则
      ];
    }

    // 耐药级别过滤
    if (resistanceClasses) {
      const classArray = Array.isArray(resistanceClasses) ? resistanceClasses : resistanceClasses.split(',');
      query.resistance_class = { $in: classArray };
    }

   
    // 导入TableA模型
    const TableA = require('./models/TableA');

    // 查询TableA数据
    const results = await TableA.find(query);

       // 提取耐药表型中的药物名称
    const excludedDrugs = new Set();

    results.forEach(record => {
      const phenotype = record.resistance_phenotype;
      if (phenotype) {
        // 解析耐药表型，提取药物名称
        // 例如: "头孢他啶-阿维巴坦-R" -> "头孢他啶-阿维巴坦"
        // 例如: "任何碳青霉烯类-I或R" -> "碳青霉烯类"
        // 例如: "阿米卡星、庆大霉素和妥布霉素-R" -> ["阿米卡星", "庆大霉素", "妥布霉素"]

        let drugNames = [];

        if (phenotype.includes('、')) {
          // 处理多个药物的情况
          const drugPart = phenotype.split('-')[0];
          drugNames = drugPart.split('、').map(name => name.replace(/和/g, '').trim());
        } else if (phenotype.includes('任何')) {
          // 处理"任何XX类"的情况
          const match = phenotype.match(/任何(.+?)类/);
          if (match) {
            drugNames = [match[1] + '类'];
          }
        } else if (phenotype.includes('第三') || phenotype.includes('第四')) {
          // 处理"第三/四代头孢菌素"的情况
          drugNames = ['头孢菌素'];
        } else {
          // 处理单个药物的情况
          const drugPart = phenotype.split('-')[0];
          if (drugPart) {
            drugNames = [drugPart.trim()];
          }
        }

        drugNames.forEach(drug => {
          if (drug) {
            excludedDrugs.add(drug);
          }
        });
      }
    });

    const excludedDrugsArray = Array.from(excludedDrugs);
   

    res.json({
      success: true,
      data: results,
      excludedDrugs: excludedDrugsArray,
      query: query
    });

  } catch (error) {
    console.error('❌ TableA查询失败:', error);
    res.status(500).json({
      success: false,
      message: 'TableA查询失败',
      error: error.message
    });
  }
});