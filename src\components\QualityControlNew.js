// QualityControlNew.js - 质量控制页面
import React, { useState, useEffect } from 'react';
import { useSharedState, useNotifications, NOTIFICATION_TYPES } from '../SharedStateContext';
import './QualityControlNew.css';

const QualityControlNew = () => {
  const { state, dispatch } = useSharedState();
  const { addNotification } = useNotifications();
  
  // 本地状态
  const [antimicrobialAgents, setAntimicrobialAgents] = useState([]);
  const [micDistributionData, setMicDistributionData] = useState([]);
  const [editingIndex, setEditingIndex] = useState(null);
  const [editingValue, setEditingValue] = useState('');
  const [counter, setCounter] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 预设的表型选择
  const [selectedPhenotypes] = useState([
    'BETA-LACTAMS',
    'IMIPENEM/CILASTATIN (+R5R OR +44 AmpC)',
    'MEROPENEM (+R5R OR +ESBLs)',
    'AMINOGLYCOSIDES',
    'RESISTANT GEN FOR TOB NET AMK'
  ]);

  const API_BASE = process.env.REACT_APP_API_BASE || 'http://localhost:5000';

  // 处理MIC值变化
  const handleMicValueChange = async (index, value, selectedAgent) => {
    if (!selectedAgent || !state.mic || !Array.isArray(state.mic) || state.mic.length === 0) {
      console.warn('Missing required data for MIC value change');
      return;
    }

    // 更新本地antimicrobialAgents状态
    setAntimicrobialAgents(prev => {
      const updated = prev.map((item, i) =>
        i === index ? { ...item, micValue: value } : item
      );
      return updated;
    });

    try {
      const bacteriaId = state.mic[0].BacteriaId;
      const bacteriaName = state.mic[0].BacteriaName;
      
      const response = await fetch(
        `${API_BASE}/api/breaking-interpret?selectedAgent=${encodeURIComponent(selectedAgent)}&micValue=${encodeURIComponent(value)}&bacteriaId=${encodeURIComponent(bacteriaId)}&bacteriaName=${encodeURIComponent(bacteriaName)}&selectedMethod=MIC`
      );

      if (response.ok) {
        const data = await response.json();
        
        if (data.sensitivity) {
          // 更新antimicrobialAgents中的sensitivity
          setAntimicrobialAgents(prev => {
            const updated = prev.map((item, i) =>
              i === index ? { ...item, sensitivity: data.sensitivity } : item
            );
            return updated;
          });

          // 更新SharedState中的mic数据
          const updatedMic = state.mic.map(m => {
            if (m.Agent === selectedAgent) {
              return {
                ...m,
                MIC: value,
                SORT: data.sensitivity
              };
            }
            return m;
          });

          // 如果没有找到对应的Agent，添加新的记录
          const found = state.mic.some(m => m.Agent === selectedAgent);
          if (!found) {
            updatedMic.push({
              Agent: selectedAgent,
              MIC: value,
              SORT: data.sensitivity,
              BacteriaId: bacteriaId,
              BacteriaName: bacteriaName,
              Comments: '',
              Remark: '',
              drugDeduction: '',
              esblAlert: '',
              expertics: [],
              selectedAuthority: 'CLSI'
            });
          }

          console.log('🔍 Quality Control更新SharedState MIC数据:', updatedMic.length, '条记录');
          dispatch({ type: 'SET_MIC', payload: updatedMic });

          // 添加通知
          addNotification(
            NOTIFICATION_TYPES.AST_RESULT_UPDATE,
            `${selectedAgent} MIC值已更新: ${value} → ${data.sensitivity}`,
            {
              antibiotic: selectedAgent,
              micValue: value,
              sensitivity: data.sensitivity
            }
          );
        }
      }
    } catch (err) {
      console.error('Failed to fetch sensitivity:', err);
      setError('更新MIC值失败');
    }
  };

  // 计算超出范围的MIC数量
  useEffect(() => {
    let cnt = 0;
    for (const item of antimicrobialAgents) {
      const micDist = micDistributionData.find(d => {
        if (!item.name || !d.displayName) return false;
        try {
          const regex = new RegExp(item.name, 'i');
          return regex.test(d.displayName);
        } catch {
          return false;
        }
      });
      
      if (micDist && item.micValue) {
        const micNum = parseFloat(item.micValue);
        if (!isNaN(micNum)) {
          if ((micDist.max !== undefined && micNum > micDist.max) || 
              (micDist.min !== undefined && micNum < micDist.min)) {
            cnt++;
          }
        }
      }
    }
    setCounter(cnt);
  }, [antimicrobialAgents, micDistributionData]);

  // 从SharedState初始化数据
  useEffect(() => {
    if (state.mic && Array.isArray(state.mic)) {
      const agents = state.mic.map((item, index) => ({
        name: item.Agent || `Agent ${index + 1}`,
        micValue: item.MIC || '',
        sensitivity: item.SORT || '-'
      }));
      console.log('🔍 Quality Control从SharedState更新数据:', agents.length, '条记录');
      setAntimicrobialAgents(agents);
    } else {
      console.log('🔍 Quality Control: SharedState中没有MIC数据');
      setAntimicrobialAgents([]);
    }
  }, [state.mic]);

  // 监听SharedState变化的调试信息
  useEffect(() => {
    console.log('🔍 Quality Control: SharedState变化', {
      hasPatient: !!state.patient,
      hasMic: !!state.mic,
      micCount: state.mic ? state.mic.length : 0
    });
  }, [state.patient, state.mic]);

  // 如果没有患者数据
  if (!state.patient) {
    return (
      <div className="quality-control-container">
        <div className="empty-state">
          <div className="empty-icon">🛡️</div>
          <div className="empty-text">还没有选择标本信息!!</div>
        </div>
      </div>
    );
  }

  // 如果没有MIC数据
  if (!state.mic || !Array.isArray(state.mic) || state.mic.length === 0) {
    return (
      <div className="quality-control-container">
        <div className="empty-state">
          <div className="empty-icon">🔧</div>
          <div className="empty-text">还没有生成报告,请转到'Mic Control',查询后，点击生成报告!!</div>
        </div>
      </div>
    );
  }

  const now = new Date();
  const pad = n => n.toString().padStart(2, '0');
  const timeStr = `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;

  return (
    <div className="quality-control-container">
      {/* 错误提示 */}
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError('')}>×</button>
        </div>
      )}

      {/* 头部状态区域 */}
      <div className="qc-header-section">
        {/* 左列 - 状态信息 */}
        <div className="qc-left-column">
          <div className="qc-field-row">
            <span className="qc-label">药敏结论:</span>
            {counter > 0 ? (
              <span className="qc-badge qc-badge-warning">需调整</span>
            ) : (
              <span className="qc-badge qc-badge-success">与修正一致</span>
            )}
          </div>
          
          <div className="qc-field-row">
            <span className="qc-label">审核状态:</span>
            <span>{counter > 0 ? '待审核' : '已审核'}</span>
          </div>
          
          <div className="qc-field-row">
            <span className="qc-label">鉴定置信度:</span>
            <span>{counter > 0 ? '等待调整' : '鉴定结果优秀'}</span>
          </div>
          
          <div className="qc-field-row">
            <span className="qc-label">分析状态:</span>
            <span>{counter > 0 ? `${timeStr} - 进行中` : `${timeStr} - 终篇`}</span>
          </div>
          
          <div className="qc-field-row">
            <span className="qc-label">表型分析:</span>
            <span>
              <input 
                type="checkbox" 
                className="qc-checkbox-warning"
                readOnly
                checked
              />
              <span className="qc-inconsistent">inconsistent</span>
            </span>
          </div>
        </div>

        {/* 右列 - 表型信息 */}
        <div className="qc-right-column">
          <div className="qc-phenotype-section">
            <span className="qc-label">已选表型:</span>
            <div className="qc-phenotype-list">
              {selectedPhenotypes.map((phenotype, index) => (
                <div key={index} className="qc-phenotype-item">
                  <input type="checkbox" checked readOnly className="qc-checkbox" />
                  <span className="qc-phenotype-text">{phenotype}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 分析提示区域 */}
      <div className="qc-analysis-section">
        <span className="qc-label">分析提示:</span>
        <div className="qc-analysis-content">
          <div className="qc-analysis-row">
            <span className="qc-analysis-label">标本编号：</span>
            <span className="qc-analysis-value">{state?.patient?.specimenNumber || '-'}</span>
          </div>
          <div className="qc-analysis-row">
            <span className="qc-analysis-label">菌落计数：</span>
            <span className="qc-analysis-value">{state?.patient?.bacteriaCount || '-'}</span>
          </div>
          <div className="qc-analysis-row">
            <span className="qc-analysis-label">培养结果：</span>
            <span className="qc-analysis-value">{state?.patient?.cultureResult || '-'}</span>
          </div>
        </div>
      </div>

      {/* BioFIT备注区域 */}
      <div className="qc-comment-section">
        <div className="qc-comment-header">
          <span className="qc-label">BioFIT 备注:</span>
          <div className="qc-comment-buttons">
            <button className="qc-btn qc-btn-primary">M1</button>
            <button className="qc-btn qc-btn-secondary">**</button>
            <button className="qc-btn qc-btn-secondary">ESC</button>
          </div>
        </div>

        <div className="qc-comment-content">
          {state.mic && Array.isArray(state.mic) && (
            <div className="qc-notes">
              {(() => {
                let totalIndex = 0;
                const notes = [];

                // 处理Comments
                const uniqueComments = [...new Set(state.mic.map(item => item.Comments))]
                  .filter(comment =>
                    comment &&
                    state.mic.some(item => comment.includes(item.selectedAuthority)) &&
                    /[\u4e00-\u9fa5]/.test(comment)
                  );

                uniqueComments.forEach(comment => {
                  totalIndex++;
                  notes.push(
                    <div key={`comment-${totalIndex}`} className="qc-note-item">
                      {totalIndex}. {comment}
                    </div>
                  );
                });

                // 处理Remarks
                const uniqueRemarks = [...new Set(state.mic.map(item => item.Remark))]
                  .filter(remark => remark);

                uniqueRemarks.forEach(remark => {
                  totalIndex++;
                  notes.push(
                    <div key={`remark-${totalIndex}`} className="qc-note-item">
                      {totalIndex}. {remark}
                    </div>
                  );
                });

                // 处理drugDeduction
                const uniqueDeductions = [...new Set(state.mic.map(item => item.drugDeduction))]
                  .filter(deduction => deduction);

                uniqueDeductions.forEach(deduction => {
                  totalIndex++;
                  const match = deduction.match(/（1）(.*?)(（2）|$)/s);
                  const shortText = match ? match[1].trim() : deduction;

                  notes.push(
                    <div key={`deduction-${totalIndex}`} className="qc-note-item">
                      {totalIndex}. {shortText}
                    </div>
                  );
                });

                // 处理esblAlert
                const uniqueAlerts = [...new Set(state.mic.map(item => item.esblAlert))]
                  .filter(alert => alert);

                uniqueAlerts.forEach(alert => {
                  totalIndex++;
                  notes.push(
                    <div key={`alert-${totalIndex}`} className="qc-note-item">
                      {totalIndex}. {alert}
                    </div>
                  );
                });

                // 处理expertics
                const uniqueExpertics = [
                  ...new Set(
                    state.mic.flatMap(item =>
                      Array.isArray(item.expertics)
                        ? item.expertics.map(expertic =>
                            JSON.stringify({
                              ABX_RESULT: expertic.ABX_RESULT,
                              MICROBIOL: expertic.MICROBIOL
                            })
                          )
                        : []
                    )
                  )
                ].filter(entry => entry);

                uniqueExpertics.forEach(entryString => {
                  totalIndex++;
                  const entry = JSON.parse(entryString);
                  notes.push(
                    <div key={`expertic-${totalIndex}`} className="qc-note-item">
                      {totalIndex}. {entry.ABX_RESULT} : {entry.MICROBIOL}
                    </div>
                  );
                });

                return notes.length > 0 ? notes : (
                  <div className="qc-note-item">暂无备注信息</div>
                );
              })()}
            </div>
          )}
        </div>
      </div>

      {/* AST表格区域 */}
      <div className="qc-table-section">
        <div className="qc-table-container">
          {antimicrobialAgents.length > 0 ? (
            <table className="qc-table">
              <thead>
                <tr>
                  <th>Antibiotic</th>
                  <th>MIC</th>
                  <th>Interp.</th>
                </tr>
              </thead>
              <tbody>
                {antimicrobialAgents.map((item, index) => {
                  // 查找对应的micDistribution数据
                  const micDist = micDistributionData.find(d => {
                    if (!item.name || !d.displayName) return false;
                    try {
                      const regex = new RegExp(item.name, 'i');
                      return regex.test(d.displayName);
                    } catch {
                      return false;
                    }
                  });

                  // 判断MIC值是否超出范围
                  let micClass = '';
                  if (micDist && item.micValue) {
                    const micNum = parseFloat(item.micValue);
                    if (!isNaN(micNum)) {
                      if ((micDist.max !== undefined && micNum > micDist.max) ||
                          (micDist.min !== undefined && micNum < micDist.min)) {
                        micClass = 'qc-mic-out-range';
                      }
                    }
                  }

                  return (
                    <tr key={index}>
                      <td className="qc-td">{item.name}</td>
                      <td
                        className={`qc-td qc-mic-cell ${micClass}`}
                        onClick={() => {
                          setEditingIndex(index);
                          setEditingValue(item.micValue || '');
                        }}
                      >
                        {editingIndex === index ? (
                          <input
                            type="text"
                            value={editingValue}
                            autoFocus
                            onChange={(e) => setEditingValue(e.target.value)}
                            onBlur={() => {
                              handleMicValueChange(index, editingValue, item.name);
                              setEditingIndex(null);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleMicValueChange(index, editingValue, item.name);
                                setEditingIndex(null);
                              }
                            }}
                            className="qc-mic-input"
                          />
                        ) : (
                          item.micValue || '-'
                        )}
                      </td>
                      <td className="qc-td">
                        <span className={`qc-sensitivity qc-sensitivity-${item.sensitivity?.toLowerCase()}`}>
                          {item.sensitivity || '-'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          ) : (
            <div className="qc-empty-table">
              暂无AST数据
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QualityControlNew;
