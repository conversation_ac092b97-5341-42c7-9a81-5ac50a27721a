const { app, BrowserWindow, ipcMain } = require('electron');
require('electron-reload')(__dirname, {
  electron: require(`${__dirname}/node_modules/electron`)
});
const path = require('path');
const { spawn } = require('child_process');

let backendProcess;

function createWindow() {
  const win = new BrowserWindow({
    width: Math.round(800 * 4 / 3), 
    height: Math.round(600 * 4 / 3),     frame: true, 
    resizable: false,             
    maximizable: false,
    minimizable: true,
    autoHideMenuBar: true,
  
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'), // 添加 preload 脚本
    },
  });

  // 加载 React 构建后的 index.html
  win.loadFile(path.join(__dirname, 'build', 'index.html'));
  win.webContents.openDevTools(); // 启动时自动打开开发者工具，便于调试
}

// 处理渲染进程的关闭窗口请求
ipcMain.handle('close-window', (event) => {
  const win = BrowserWindow.fromWebContents(event.sender);
  if (win) win.close();
});

app.whenReady().then(() => {
  // 启动 Node 后端服务
  backendProcess = spawn(process.execPath, [path.join(__dirname, 'backend', 'server.js')], {
    cwd: path.join(__dirname, 'backend'),
    stdio: 'inherit',
  });

  backendProcess.on('error', (err) => {
    console.error('Failed to start backend server:', err);
  });

  createWindow();
});

app.on('window-all-closed', () => {
  if (backendProcess) {
    backendProcess.kill();
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
