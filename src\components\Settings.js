// Settings.js - 字典表维护页面
import { useState, useEffect } from 'react';

function Settings() {
  const [selectedTable, setSelectedTable] = useState('t_dict_antibiotics');
  const [dictData, setDictData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);

  // 字典表列表
  const dictTables = [
    { key: 't_dict_antibiotics', name: 'Antibiotics Dictionary', description: '抗生素字典' },
    { key: 't_dict_organisms', name: 'Organisms Dictionary', description: '微生物字典' },
    { key: 't_dict_sample_types', name: 'Sample Types Dictionary', description: '样本类型字典' },
    { key: 't_dict_departments', name: 'Departments Dictionary', description: '科室字典' },
    { key: 't_dict_physicians', name: 'Physicians Dictionary', description: '医生字典' },
    { key: 't_dict_test_methods', name: 'Test Methods Dictionary', description: '检测方法字典' },
    { key: 't_dict_resistance_patterns', name: 'Resistance Patterns Dictionary', description: '耐药模式字典' },
    { key: 't_dict_quality_controls', name: 'Quality Controls Dictionary', description: '质控标准字典' }
  ];

  // 模拟字典数据
  const mockDictData = {
    't_dict_antibiotics': [
      { _id: '1', code: 'AMP', name: 'Ampicillin', category: 'Beta-lactam', status: 'Active', sort_order: 1 },
      { _id: '2', code: 'AMC', name: 'Amoxicillin/Clavulanate', category: 'Beta-lactam', status: 'Active', sort_order: 2 },
      { _id: '3', code: 'CIP', name: 'Ciprofloxacin', category: 'Fluoroquinolone', status: 'Active', sort_order: 3 },
      { _id: '4', code: 'GEN', name: 'Gentamicin', category: 'Aminoglycoside', status: 'Active', sort_order: 4 },
      { _id: '5', code: 'VAN', name: 'Vancomycin', category: 'Glycopeptide', status: 'Inactive', sort_order: 5 }
    ],
    't_dict_organisms': [
      { _id: '1', code: 'ECOLI', name: 'Escherichia coli', gram_type: 'Negative', status: 'Active', sort_order: 1 },
      { _id: '2', code: 'STAPH', name: 'Staphylococcus aureus', gram_type: 'Positive', status: 'Active', sort_order: 2 },
      { _id: '3', code: 'KLEB', name: 'Klebsiella pneumoniae', gram_type: 'Negative', status: 'Active', sort_order: 3 },
      { _id: '4', code: 'PSEUDO', name: 'Pseudomonas aeruginosa', gram_type: 'Negative', status: 'Active', sort_order: 4 }
    ],
    't_dict_sample_types': [
      { _id: '1', code: 'URINE', name: 'Urine', description: '尿液样本', status: 'Active', sort_order: 1 },
      { _id: '2', code: 'BLOOD', name: 'Blood', description: '血液样本', status: 'Active', sort_order: 2 },
      { _id: '3', code: 'SPUTUM', name: 'Sputum', description: '痰液样本', status: 'Active', sort_order: 3 },
      { _id: '4', code: 'WOUND', name: 'Wound', description: '伤口分泌物', status: 'Active', sort_order: 4 }
    ]
  };

  // 加载字典数据
  const loadDictData = async (tableName) => {
    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      const data = mockDictData[tableName] || [];
      setDictData(data);
    } catch (error) {
      console.error('Failed to load dictionary data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadDictData(selectedTable);
  }, [selectedTable]);

  // 处理表格选择
  const handleTableChange = (tableName) => {
    setSelectedTable(tableName);
    setEditingItem(null);
    setShowAddForm(false);
  };

  // 处理编辑
  const handleEdit = (item) => {
    setEditingItem({ ...item });
    setShowAddForm(false);
  };

  // 处理删除
  const handleDelete = (id) => {
    if (window.confirm('确定要删除这条记录吗？')) {
      setDictData(prev => prev.filter(item => item._id !== id));
    }
  };

  // 处理保存
  const handleSave = (item) => {
    if (editingItem) {
      // 更新现有项
      setDictData(prev => prev.map(d => d._id === item._id ? item : d));
    } else {
      // 添加新项
      const newItem = { ...item, _id: Date.now().toString() };
      setDictData(prev => [...prev, newItem]);
    }
    setEditingItem(null);
    setShowAddForm(false);
  };

  // 获取当前表的列配置
  const getTableColumns = (tableName) => {
    const commonColumns = ['code', 'name', 'status', 'sort_order'];
    const specificColumns = {
      't_dict_antibiotics': ['category'],
      't_dict_organisms': ['gram_type'],
      't_dict_sample_types': ['description'],
      't_dict_departments': ['description'],
      't_dict_physicians': ['title', 'department'],
      't_dict_test_methods': ['description', 'equipment'],
      't_dict_resistance_patterns': ['pattern_type', 'description'],
      't_dict_quality_controls': ['control_type', 'expected_range']
    };
    return [...commonColumns, ...(specificColumns[tableName] || [])];
  };

  return (
    <div className="h-full bg-gray-50 p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Dictionary Tables Management - 字典表维护</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 左侧：字典表列表 */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Dictionary Tables</h2>
            <div className="space-y-2">
              {dictTables.map((table) => (
                <button
                  key={table.key}
                  onClick={() => handleTableChange(table.key)}
                  className={`w-full text-left p-3 rounded-md transition-colors ${
                    selectedTable === table.key
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'hover:bg-gray-50 text-gray-700'
                  }`}
                >
                  <div className="font-medium text-sm">{table.name}</div>
                  <div className="text-xs text-gray-500 mt-1">{table.description}</div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧：数据表格和操作 */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* 表格头部 */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {dictTables.find(t => t.key === selectedTable)?.name}
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {dictTables.find(t => t.key === selectedTable)?.description}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowAddForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    + Add New
                  </button>
                  <button
                    onClick={() => loadDictData(selectedTable)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    🔄 Refresh
                  </button>
                </div>
              </div>
            </div>

            {/* 数据表格 */}
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="p-8 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <p className="mt-2 text-gray-600">Loading...</p>
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {getTableColumns(selectedTable).map((column) => (
                        <th
                          key={column}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {column.replace('_', ' ')}
                        </th>
                      ))}
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {dictData.map((item) => (
                      <tr key={item._id} className="hover:bg-gray-50">
                        {getTableColumns(selectedTable).map((column) => (
                          <td key={column} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {column === 'status' ? (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                item[column] === 'Active'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {item[column]}
                              </span>
                            ) : (
                              item[column] || '-'
                            )}
                          </td>
                        ))}
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleEdit(item)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDelete(item._id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>

            {/* 分页 */}
            <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-700">
                  Showing {dictData.length} entries
                </p>
                <div className="flex space-x-2">
                  <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-100">
                    Previous
                  </button>
                  <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-100">
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 编辑/添加表单模态框 */}
      {(editingItem || showAddForm) && (
        <DictItemForm
          item={editingItem}
          tableName={selectedTable}
          columns={getTableColumns(selectedTable)}
          onSave={handleSave}
          onCancel={() => {
            setEditingItem(null);
            setShowAddForm(false);
          }}
        />
      )}
    </div>
  );
}

// 字典项编辑表单组件
function DictItemForm({ item, tableName, columns, onSave, onCancel }) {
  const [formData, setFormData] = useState(
    item || {
      code: '',
      name: '',
      status: 'Active',
      sort_order: 1
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {item ? 'Edit' : 'Add'} Dictionary Item
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          {columns.map((column) => (
            <div key={column}>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {column.replace('_', ' ').toUpperCase()}
              </label>
              {column === 'status' ? (
                <select
                  value={formData[column] || 'Active'}
                  onChange={(e) => handleChange(column, e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                </select>
              ) : column === 'sort_order' ? (
                <input
                  type="number"
                  value={formData[column] || 1}
                  onChange={(e) => handleChange(column, parseInt(e.target.value))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min="1"
                />
              ) : (
                <input
                  type="text"
                  value={formData[column] || ''}
                  onChange={(e) => handleChange(column, e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required={['code', 'name'].includes(column)}
                />
              )}
            </div>
          ))}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Settings;
