.lab-report-container {
  width: 100%;
   max-width: 1580px;
    padding: 20px;
  background-color: #f8f8f8;
  font-family: Arial, Helvetica, sans-serif;
  color: #333;
  font-size: 14px;
}
.lab-report-header-section {
  background-color: #D4D4D4;
  padding: 8px;
  border-bottom: 1px solid #999;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}
.lab-report-left-column,
.lab-report-right-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.lab-report-field-row {
  display: flex;
  align-items: center;
  gap: 16px;
}
.lab-report-label {
  font-weight: bold;
  min-width: 120px;
}
.lab-report-input {
  border: 1px solid #999;
  padding: 2px 6px;
  background-color: white;
  font-size: 14px;
}
.lab-report-select {
  border: 1px solid #999;
  padding: 2px 6px;
  background-color: white;
  font-size: 14px;
  width: 150px;
}
.lab-report-blue-button {
  background-color: #0066CC;
  color: white;
  padding: 2px 8px;
  border: none;
  font-size: 10px;
  cursor: pointer;
}
.lab-report-green-badge {
  background-color: #90EE90;
  padding: 2px 8px;
  font-size: 10px;
  border: 1px solid #999;
}
.lab-report-phenotype-list {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.lab-report-phenotype-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.lab-report-checkbox {
  width: 12px;
  height: 12px;
}
.lab-report-messages-section {
  background-color: #D4D4D4;
  padding: 8px;
  border-bottom: 1px solid #999;
  display: flex;
  align-items: center;
  gap: 8px;
}
.lab-report-comment-section {
  background-color: #D4D4D4;
  padding: 8px;
  border-bottom: 1px solid #999;
}
.lab-report-comment-text {
  margin-top: 8px;
  font-size: 10px;
  background-color: #F0F0F0;
  padding: 8px;
  border: 1px solid #999;
}
.lab-report-gray-button {
  background-color: #999;
  color: white;
  padding: 2px 8px;
  border: none;
  font-size: 10px;
  cursor: pointer;
}
.lab-report-table-section {
  flex: 1;
  background-color: #D4D4D4;
  padding: 8px;
}
.lab-report-table-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  height: calc(100% - 30px);
}
.lab-report-table {
  background-color: white;
  border: 1px solid #999;
  width: 100%;
  border-collapse: collapse;
}
.lab-report-table-header {
  background-color: #E0E0E0;
}
.lab-report-th {
  border: 1px solid #CCC;
  padding: 4px;
  font-size: 10px;
  font-weight: bold;
}
.lab-report-td {
  border: 1px solid #CCC;
  padding: 4px;
  font-size: 10px;
}
.lab-report-resistant-text {
  color: #CC0000;
  font-weight: bold;
}
.lab-report-sensitive-text {
  color: #006600;
  font-weight: bold;
}
.lab-report-table-row {
  cursor: pointer;
}
.lab-report-table-row-hover {
  background-color: #F5F5F5;
}
.lab-report-mic-green {
  color: green;
  font-weight: bold;
}
.lab-report-red-badge {
  color: red;
  font-weight: bold;
}
