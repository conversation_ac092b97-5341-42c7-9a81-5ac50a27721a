import React, { createContext, useContext, useState } from 'react';

// 创建通知上下文
const NotificationContext = createContext();

// 通知类型
export const NOTIFICATION_TYPES = {
  DRUG_FILTER_CHANGE: 'DRUG_FILTER_CHANGE',
  AST_RESULT_UPDATE: 'AST_RESULT_UPDATE',
  BREAKING_POINT_MISSING: 'BREAKING_POINT_MISSING'
};

// 通知提供者组件
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  // 添加通知
  const addNotification = (type, message, data = {}) => {
    const notification = {
      id: Date.now() + Math.random(),
      type,
      message,
      data,
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => [notification, ...prev]);
    
    console.log('🔔 添加通知:', notification);
    return notification.id;
  };

  // 标记通知为已读
  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // 标记所有通知为已读
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // 删除通知
  const removeNotification = (notificationId) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  // 清空所有通知
  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // 获取未读通知数量
  const getUnreadCount = () => {
    return notifications.filter(notification => !notification.read).length;
  };

  // 获取特定类型的通知
  const getNotificationsByType = (type) => {
    return notifications.filter(notification => notification.type === type);
  };

  const value = {
    notifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    getUnreadCount,
    getNotificationsByType
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// 使用通知的Hook
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
