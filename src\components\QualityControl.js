// QualityControl.js - 质量控制表型图形化表示页面
import { useState, useEffect } from 'react';
import './QualityControl.css';

function QualityControl() {
  const [selectedOrganism, setSelectedOrganism] = useState('Klebsiella pneumoniae ssp pneumoniae');
  const [selectedSpecies, setSelectedSpecies] = useState('Klebsiella pneumoniae ssp pneumoniae');
  const [selectedFamily, setSelectedFamily] = useState('BETA-LACTAMS');

  // 抗生素数据
  const antibiotics = [
    'ESBLs (esbl+)',
    'Amikacin (amk)',
    'Ampicillin (amp)',
    'Ampicillin/Clavulanic Acid (amc)',
    'Ampicillin/Sulbactam (sam)',
    'Ticarcillin (tic)',
    'Ticarcillin/Clavulanic Acid (tcc)',
    'Piperacillin (pip)',
    'Piperacillin/Tazobactam (tzp)',
    'Cefazolin (cfz)',
    'Cefuroxime (cxm)',
    'Cefotaxime (ctx)',
    'Ceftazidime (caz)',
    'Ceftriaxone (cro)',
    'Cefepime (fep)',
    'Ceftaroline (cpt)',
    'Ceftolozane (c/t)',
    'Ceftazidime (caz)',
    'Doripenem (dor)',
    'Ertapenem (etp)',
    'Imipenem (ipm)',
    'Meropenem (mem)'
  ];

  // 表型类别
  const phenotypeCategories = [
    'WILD PENICILLINASE',
    'CARBAPENEMASE (A OR B)',
    'IMPERMEABILITY CARBA (≤ESBLs OR ≤HLA AmpC)',
    'ACI PHAGE 1 IMPERMEABILITY (≤ESBL/AmpC)',
    'ACQUIRED CEPHALOSPORINASE/ESBL',
    'ACQUIRED BETA-LACTAMASE'
  ];

  // 模拟表型数据 - 每个抗生素对每个表型类别的结果
  const phenotypeData = {};
  
  // 初始化数据
  useEffect(() => {
    // 为每个抗生素和表型类别生成随机数据
    antibiotics.forEach(antibiotic => {
      phenotypeData[antibiotic] = {};
      phenotypeCategories.forEach(category => {
        // 随机生成表型结果：0=白色(无), 1=蓝色(阴性), 2=粉色(阳性), 3=深蓝(强阳性)
        phenotypeData[antibiotic][category] = Math.floor(Math.random() * 4);
      });
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 获取单元格颜色
  const getCellColor = (value) => {
    switch (value) {
      case 0: return 'cell-white';      // 白色 - 无结果
      case 1: return 'cell-blue';       // 蓝色 - 阴性
      case 2: return 'cell-pink';       // 粉色 - 阳性  
      case 3: return 'cell-dark-blue';  // 深蓝 - 强阳性
      default: return 'cell-white';
    }
  };

  // 获取表型评级星星
  const getPhenotypeStars = (category) => {
    // 根据类别返回不同的星级评定
    const ratings = {
      'WILD PENICILLINASE': 3,
      'CARBAPENEMASE (A OR B)': 3,
      'IMPERMEABILITY CARBA (≤ESBLs OR ≤HLA AmpC)': 2,
      'ACI PHAGE 1 IMPERMEABILITY (≤ESBL/AmpC)': 1,
      'ACQUIRED CEPHALOSPORINASE/ESBL': 2,
      'ACQUIRED BETA-LACTAMASE': 1
    };
    
    const rating = ratings[category] || 0;
    return '★'.repeat(rating);
  };

  return (
    <div className="quality-control-container">
      {/* 页面标题 */}
      <div className="page-header">
        <h1 className="page-title">Quality Control</h1>
        <h2 className="page-subtitle">Phenotype Graphical Representation</h2>
      </div>

      {/* 选择器区域 */}
      <div className="selector-section">
        <div className="selector-row">
          <label className="selector-label">Organism:</label>
          <select 
            value={selectedOrganism} 
            onChange={(e) => setSelectedOrganism(e.target.value)}
            className="organism-selector"
          >
            <option value="Klebsiella pneumoniae ssp pneumoniae">Klebsiella pneumoniae ssp pneumoniae</option>
            <option value="Escherichia coli">Escherichia coli</option>
            <option value="Staphylococcus aureus">Staphylococcus aureus</option>
          </select>
        </div>

        <div className="selector-row">
          <label className="selector-label">Species with phenotypes:</label>
          <select 
            value={selectedSpecies} 
            onChange={(e) => setSelectedSpecies(e.target.value)}
            className="species-selector"
          >
            <option value="Klebsiella pneumoniae ssp pneumoniae">Klebsiella pneumoniae ssp pneumoniae</option>
          </select>
        </div>

        <div className="selector-row">
          <label className="selector-label">Antibiotic families:</label>
          <select 
            value={selectedFamily} 
            onChange={(e) => setSelectedFamily(e.target.value)}
            className="family-selector"
          >
            <option value="BETA-LACTAMS">BETA-LACTAMS</option>
            <option value="AMINOGLYCOSIDES">AMINOGLYCOSIDES</option>
            <option value="FLUOROQUINOLONES">FLUOROQUINOLONES</option>
          </select>
        </div>
      </div>

      {/* 表型图表 */}
      <div className="phenotype-chart">
        <div className="chart-container">
          {/* 表头 */}
          <div className="chart-header">
            <div className="antibiotic-header">Antibiotic</div>
            {phenotypeCategories.map((category, index) => (
              <div key={index} className="category-header">
                <div className="category-title">{category}</div>
                <div className="category-stars">{getPhenotypeStars(category)}</div>
              </div>
            ))}
          </div>

          {/* 数据行 */}
          <div className="chart-body">
            {antibiotics.map((antibiotic, rowIndex) => (
              <div key={rowIndex} className="chart-row">
                <div className="antibiotic-name">{antibiotic}</div>
                {phenotypeCategories.map((category, colIndex) => {
                  const value = Math.floor(Math.random() * 4); // 实时生成随机数据
                  return (
                    <div 
                      key={colIndex} 
                      className={`phenotype-cell ${getCellColor(value)}`}
                      title={`${antibiotic} - ${category}: ${value}`}
                    >
                      {value === 2 && <div className="pink-indicator">▲</div>}
                      {value === 3 && <div className="blue-indicator">●</div>}
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 图例 */}
      <div className="legend-section">
        <div className="legend-container">
          <div className="legend-item">
            <div className="legend-color cell-white"></div>
            <span>No Result</span>
          </div>
          <div className="legend-item">
            <div className="legend-color cell-blue"></div>
            <span>Negative</span>
          </div>
          <div className="legend-item">
            <div className="legend-color cell-pink"></div>
            <span>Positive</span>
          </div>
          <div className="legend-item">
            <div className="legend-color cell-dark-blue"></div>
            <span>Strong Positive</span>
          </div>
        </div>

        <div className="phenotype-legend">
          <div className="legend-title">Phenotype Rating:</div>
          <div className="rating-item">
            <span className="stars green-stars">★★★</span>
            <span>Best phenotype</span>
          </div>
          <div className="rating-item">
            <span className="stars yellow-stars">★★</span>
            <span>Recognized phenotype</span>
          </div>
          <div className="rating-item">
            <span className="stars gray-stars">★</span>
            <span>Possible phenotype</span>
          </div>
          <div className="rating-item">
            <span className="typical-label">Very Typical</span>
          </div>
          <div className="rating-item">
            <span className="typical-label">Typical</span>
          </div>
          <div className="rating-item">
            <span className="typical-label">Not Very Typical</span>
          </div>
        </div>
      </div>

      {/* 底部控制栏 */}
      <div className="bottom-controls">
        <div className="control-buttons">
          <button className="control-btn">◀</button>
          <span className="page-indicator">1</span>
          <button className="control-btn">▶</button>
        </div>
        <div className="zoom-controls">
          <button className="zoom-btn">-</button>
          <span className="zoom-level">100%</span>
          <button className="zoom-btn">+</button>
        </div>
      </div>
    </div>
  );
}

export default QualityControl;
