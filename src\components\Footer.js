// import React from 'react'; // Not needed in React 17+

function Footer() {
  return (
    <footer className="w-full bg-gradient-to-r from-gray-800 to-gray-900 text-white py-4 px-6 border-t border-gray-700">
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-6">
          <span className="font-medium">© 2024 bioMérieux</span>
          <span className="text-gray-400">|</span>
          <span className="text-gray-300">AES Version 3.2.1</span>
          <span className="text-gray-400">|</span>
          <span className="text-gray-300">Build 2024.07.25</span>
        </div>

        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              <span className="text-green-300 font-medium">System Online</span>
            </span>
            <span className="text-gray-400">|</span>
            <span className="text-gray-300">
              Last Update: {new Date().toLocaleString()}
            </span>
          </div>

          <div className="flex items-center space-x-3">
            <button className="text-gray-400 hover:text-white transition-colors">
              <span className="text-sm">📞 Support</span>
            </button>
            <button className="text-gray-400 hover:text-white transition-colors">
              <span className="text-sm">📖 Help</span>
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;