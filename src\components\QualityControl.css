/* QualityControl.css - 质量控制表型图形化表示页面样式 */

.quality-control-container {
  height: 100vh;
  background: #f0f4f8;
  padding: 1rem;
  overflow: auto;
}

.page-header {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2d3748;
  margin: 0;
}

.page-subtitle {
  font-size: 1.125rem;
  color: #4a5568;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

/* 选择器区域 */
.selector-section {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selector-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.selector-row:last-child {
  margin-bottom: 0;
}

.selector-label {
  font-weight: 500;
  color: #2d3748;
  min-width: 150px;
  font-size: 0.875rem;
}

.organism-selector,
.species-selector,
.family-selector {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  background: white;
  font-size: 0.875rem;
}

/* 表型图表 */
.phenotype-chart {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.chart-container {
  min-width: 1200px;
}

.chart-header {
  display: grid;
  grid-template-columns: 250px repeat(6, 1fr);
  gap: 1px;
  background: #e2e8f0;
  border: 1px solid #cbd5e0;
}

.antibiotic-header {
  background: #4a5568;
  color: white;
  padding: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.category-header {
  background: #4a5568;
  color: white;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.75rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 60px;
}

.category-title {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.category-stars {
  color: #ffd700;
  font-size: 0.875rem;
}

.chart-body {
  border: 1px solid #cbd5e0;
  border-top: none;
}

.chart-row {
  display: grid;
  grid-template-columns: 250px repeat(6, 1fr);
  gap: 1px;
  background: #e2e8f0;
}

.chart-row:nth-child(even) {
  background: #f7fafc;
}

.antibiotic-name {
  background: white;
  padding: 0.5rem;
  font-size: 0.75rem;
  color: #2d3748;
  display: flex;
  align-items: center;
  border-right: 1px solid #e2e8f0;
}

.phenotype-cell {
  background: white;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.phenotype-cell:hover {
  transform: scale(1.1);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 单元格颜色 */
.cell-white {
  background: #ffffff;
}

.cell-blue {
  background: #3182ce;
}

.cell-pink {
  background: #ed64a6;
}

.cell-dark-blue {
  background: #2c5282;
}

/* 指示器 */
.pink-indicator {
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: bold;
}

.blue-indicator {
  color: #ffffff;
  font-size: 1rem;
}

/* 图例区域 */
.legend-section {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 2rem;
}

.legend-container {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.legend-color {
  width: 20px;
  height: 20px;
  border: 1px solid #cbd5e0;
  border-radius: 3px;
}

.phenotype-legend {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.legend-title {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #4a5568;
}

.stars {
  font-size: 0.875rem;
}

.green-stars {
  color: #38a169;
}

.yellow-stars {
  color: #d69e2e;
}

.gray-stars {
  color: #a0aec0;
}

.typical-label {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background: #e2e8f0;
  color: #4a5568;
}

/* 底部控制栏 */
.bottom-controls {
  background: white;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.control-btn {
  background: #4a5568;
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.control-btn:hover {
  background: #2d3748;
}

.page-indicator {
  font-weight: 500;
  color: #4a5568;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.zoom-btn {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.zoom-btn:hover {
  background: #cbd5e0;
}

.zoom-level {
  font-size: 0.875rem;
  color: #4a5568;
  min-width: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .chart-container {
    min-width: 800px;
  }
  
  .category-header {
    font-size: 0.625rem;
    padding: 0.25rem;
  }
  
  .antibiotic-name {
    font-size: 0.625rem;
  }
  
  .legend-section {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .quality-control-container {
    padding: 0.5rem;
  }
  
  .selector-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .selector-label {
    min-width: auto;
  }
  
  .organism-selector,
  .species-selector,
  .family-selector {
    width: 100%;
  }
}
